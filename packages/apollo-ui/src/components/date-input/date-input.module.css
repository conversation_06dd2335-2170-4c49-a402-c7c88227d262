@layer legacy {
    .dateInputCalendarHeader {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: row;
        gap: var(--apl-space-gap-xs, 8px);
        align-items: center;
        justify-content: space-around;
    }

    .dateInputMonthButton,
    .dateInputYearButton {
        width: fit-content;
        color: var(--apl-colors-content-default, #0C0E11);
        padding: 0;
        padding-left: var(--apl-space-padding-xs, 8px);
        padding-right: var(--apl-space-padding-xs, 8px);
    }

    .dateInputPrevMonthButton,
    .dateInputNextMonthButton {
        color: var(--apl-colors-content-default, #0C0E11);
    }

    /* React DateInput overrides */
    :global(.react-datepicker) {
        border: 1px solid var(--apl-colors-border-default, #D3D7E1);
        background-color: var(--apl-colors-surface-static-ui-default, #FFFFFF);
        border-radius: 8px;
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
        font-family: var(--apl-font-font-family-body1);
        min-width: 270px;
        padding: 12px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
    }

    :global(.react-datepicker__week) {
        display: flex;
        align-items: center;
        align-self: stretch;
    }

    :global(.react-datepicker__header) {
        background-color: var(--apl-colors-surface-static-ui-default, #FFFFFF);
        padding-top: var(--apl-space-padding-xs, 8px);
        font-size: var(--apl-typography-body2-font-size);
        font-weight: var(--apl-typography-body2-font-weight);
        font-family: var(--font-ibm-plex-sans-thai);
        margin: 0;
        margin-bottom: var(--apl-space-padding-2xs, 4px);
    }


    :global(.react-datepicker__day-names) {
        color: var(--apl-colors-content-description, #5C6372);
        font-size: var(--apl-font-font-size-caption);
        margin: 0;
        padding-top: 8px;
        border-bottom: none;
    }

    :global(.react-datepicker__day--outside-month) {
        color: var(--apl-colors-content-disabled);
    }

    :global(.react-datepicker__day-name) {
        color: var(--apl-colors-content-description, #5C6372);
        font-size: var(--apl-font-font-size-caption);
        margin: var(--apl-space-padding-2xs, 4px);
    }

    :global(.react-datepicker__day) {
        padding: 0;
        margin: var(--apl-space-padding-2xs, 4px);
        border-radius: 28px;
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    :global(.react-datepicker__day:hover) {
        background-color: var(--apl-colors-surface-static-ui-hover, #F6F7FB);
        color: var(--apl-colors-content-default, #0C0E11);
        border-radius: 28px;
    }

    :global(.react-datepicker__day--today),
    :global(.react-datepicker__day--keyboard-selected.react-datepicker__day--today) {
        border: 1px solid var(--apl-colors-content-primary-default, #006D2E);
        border-radius: 28px;
        color: var(--apl-colors-content-primary-default, #006D2E);
        background-color: transparent;
        text-decoration-line: none;
    }

    :global(.react-datepicker__day--today:hover) {
        background-color: var(--apl-colors-surface-static-primary-hover, #D5F0E0);
    }

    :global(.react-datepicker__day--in-range),
    :global(.react-datepicker__day--in-selecting-range),
    :global(.react-datepicker__day--in-range:hover),
    :global(.react-datepicker__day--keyboard-selected.react-datepicker__day--in-range),
    :global(.react-datepicker__day--in-range.react-datepicker__day--today),
    :global(.react-datepicker__day--in-range.react-datepicker__day--today:hover) {
        background-color: var(--apl-colors-surface-static-primary-default, #F5FFF7);
        color: var(--apl-colors-content-default, #0C0E11);
        border-radius: 0;
        border: none;
        width: 2rem;
        margin-left: 0;
        margin-right: 0;
        padding: 0;
        padding-left: 4px;
        padding-right: 4px;
    }

    :global(.react-datepicker__day--selected),
    :global(.react-datepicker__day--selected:hover) {
        background-color: var(--apl-colors-surface-action-primary-default, #006D2E);
        color: var(--apl-colors-content-inversed, #FFFFFF);
    }

    :global(.react-datepicker__day--keyboard-selected) {
        background-color: var(--apl-colors-surface-static-process-default, #EDF0FE);
        color: var(--apl-colors-content-default, #0C0E11);
    }



    :global(.react-datepicker__day--in-selecting-range),
    :global(.react-datepicker__day--in-selecting-range:hover) {
        background-color: var(--apl-colors-surface-static-primary-hover, #D5F0E0);
        color: var(--apl-colors-content-default, #0C0E11);
        border-radius: 0;
    }

    :global(.react-datepicker__day--disabled) {
        color: var(--apl-colors-content-disabled, #BEC4D1);
        background-color: var(--apl-colors-surface-static-ui-disabled, #F6F7FB);
    }

    :global(.react-datepicker__day--disabled:hover) {
        background-color: var(--apl-colors-surface-static-ui-disabled, #F6F7FB);
    }

    :global(.react-datepicker__day--today.react-datepicker__day--disabled) {
        outline-color: var(--apl-colors-content-disabled, #BEC4D1);
        color: var(--apl-colors-content-disabled, #BEC4D1);
    }

    :global(react-datepicker__day--in-range.react-datepicker__day--disabled),
    :global(.react-datepicker__day--in-range.react-datepicker__day--disabled:hover) {
        color: var(--apl-colors-content-disabled);
        background-color: var(--apl-colors-surface-static-ui-disabled);
    }

    :global(.react-datepicker__month-text),
    :global(.react-datepicker__year-text) {
        background-color: var(--apl-colors-surface-static-ui-hover, #F6F7FB);
        color: var(--apl-colors-content-default, #0C0E11);
        padding: var(--apl-space-padding-xs, 8px);
        margin: var(--apl-space-padding-2xs, 4px);
        border-radius: 4px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }


    :global(.react-datepicker__month-text:hover),
    :global(.react-datepicker__year-text:hover) {
        background-color: var(--apl-colors-surface-static-primary-hover, #D5F0E0);
        color: var(--apl-colors-content-default, #0C0E11);
    }

    :global(.react-datepicker__month-text--selected),
    :global(.react-datepicker__month-text--selected:hover),
    :global(.react-datepicker__year-text--selected),
    :global(.react-datepicker__year-text--selected:hover) {
        background-color: var(--apl-colors-surface-action-primary-default, #006D2E);
        color: var(--apl-colors-content-inversed, #FFFFFF);
    }

    :global(.react-datepicker__day--selecting-range-end),
    :global(.react-datepicker__day--selecting-range-start),
    :global(.react-datepicker__day--selecting-range-end:hover),
    :global(.react-datepicker__day--selecting-range-start:hover),
    :global(.react-datepicker__day--range-end.react-datepicker__day--in-range),
    :global(.react-datepicker__day--range-start.react-datepicker__day--in-range) {
        background-color: var(--apl-colors-surface-static-success-default);
        color: var(--apl-colors-content-primary-default);
    }

    :global(.react-datepicker__month-text--disabled:hover),
    :global(.react-datepicker__year-text--disabled:hover) {
        background-color: transparent;
        color: var(--apl-colors-content-disabled, #BEC4D1);
    }

    .inputEndDecorator {
        color: var(--apl-colors-content-description, #5C6372);
    }

    .inputEndDecorator.disabled {
        color: var(--apl-colors-content-disabled, #BEC4D1);
    }

    :global(.react-datepicker__day--range-start),
    :global(.react-datepicker__day--range-start:hover) {
        border-radius: 0;
        border-top-left-radius: 16px;
        border-bottom-left-radius: 16px;
        margin-left: 0.166rem;
        outline: 0;

    }

    :global(.react-datepicker__day--range-end),
    :global(.react-datepicker__day--range-end:hover) {
        border-radius: 0;
        border-top-right-radius: 16px;
        border-bottom-right-radius: 16px;
        margin-right: 0.166rem;
        outline: 0;
    }

    :global(.react-datepicker__day--selecting-range-start),
    :global(.react-datepicker__day--selecting-range-start:hover) {
        border-top-left-radius: 16px;
        border-bottom-left-radius: 16px;
    }

    :global(.react-datepicker__day--selecting-range-end),
    :global(.react-datepicker__day--selecting-range-end:hover) {
        border-top-right-radius: 16px;
        border-bottom-right-radius: 16px;
    }

    :global(.react-datepicker__monthPicker),
    :global(.react-datepicker__year) {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        gap: 12px;
    }

    :global(.react-datepicker__month-wrapper) {
        width: 100%;
        display: flex;
        justify-content: space-between;
        row-gap: 12px;
    }

    :global(.react-datepicker__year-wrapper) {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        max-width: 100%;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        justify-items: center;
        gap: 12px;
    }

    .dateInputDay {
        text-align: center;
        vertical-align: middle;
        padding: 5px;
    }

}

@layer apollo {
    .dateInputCalendarHeader {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
    }

    .dateInputMonthButton,
    .dateInputYearButton {
        width: fit-content;
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647) !important;
        padding: var(--apl-alias-spacing-padding-padding4, 6px);
    }

    .dateInputPrevMonthButton,
    .dateInputNextMonthButton {
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647) !important;
    }

    /* React DateInput overrides */
    :global(.react-datepicker) {
        border: none;
        background-color: var(--apl-alias-color-background-and-surface-background, #FFF);
        border-radius: 8px;
        box-shadow: 4px 4px 15px 3px rgba(0, 0, 0, 0.05);
        font-family: var(--apl-alias-typography-label-large-font-family);
        font-weight: var(--apl-alias-typography-label-large-weight-emphasized);
        min-width: 304px;
        width: 100%;
        padding: 12px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        align-self: stretch;
    }

    :global(.react-datepicker__header) {
        background-color: var(--apl-alias-color-background-and-surface-background, #FFF);
        padding: 0;
        margin-bottom: var(--apl-alias-spacing-padding-padding5, 8px);

    }

    :global(.react-datepicker__day-names) {
        color: var(--apl-alias-color-background-and-surface-on-surface-variant, #C8C6C6);
        font-family: var(--apl-alias-typography-label-medium-font-family);
        font-size: var(--apl-alias-typography-label-medium-font-size);
        font-weight: var(--apl-alias-typography-label-medium-weight-emphasized);
        margin: 0;
        padding-top: 0px;
        border-bottom: 0.5px solid var(--apl-alias-color-outline-and-border-outline, #ADABAB);
        display: flex;
        align-items: flex-start;
        align-self: stretch;
    }

    :global(.react-datepicker__day-name) {
        color: var(--apl-alias-color-background-and-surface-on-surface-variant, #C8C6C6);
        margin: var(--apl-alias-spacing-padding-padding6, 10px);
        padding: 0;
        flex: 1;
        text-align: center;
    }

    :global(.react-datepicker__week) {
        display: flex;
        align-items: center;
        align-self: stretch;
    }

    :global(.react-datepicker__month),
    :global(.react-datepicker__year) {
        margin: 0;
    }

    :global(.react-datepicker__day) {
        border-radius: var(--apl-alias-radius-radius1, 0px);
        padding: 0;
        margin: 2px var(--apl-alias-spacing-padding-padding3, 4px);
        flex: 1;
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        background-color: transparent;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    :global(.react-datepicker__day--outside-month) {
        color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    }

    :global(.react-datepicker__day--outside-decade) {
        color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    }

    :global(.react-datepicker__day:hover) {
        background-color: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        border-radius: var(--apl-alias-radius-radius11, 9999px);
    }

    :global(.react-datepicker__day--today),
    :global(.react-datepicker__day--keyboard-selected.react-datepicker__day--today) {
        border: none;
        color: var(--apl-alias-color-primary-primary, #016E2E);
        text-decoration-line: underline;
        text-decoration-style: solid;
        text-decoration-skip-ink: auto;
        text-decoration-thickness: auto;
        text-underline-offset: auto;
        background-color: transparent;
        border-radius: var(--apl-alias-radius-radius1, 0px);
    }

    :global(.react-datepicker__day--today:hover) {
        background-color: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        border-radius: var(--apl-alias-radius-radius11, 9999px);
    }

    :global(.react-datepicker__day--in-range),
    :global(.react-datepicker__day--in-selecting-range),
    :global(.react-datepicker__day--in-range:hover),
    :global(.react-datepicker__day--keyboard-selected.react-datepicker__day--in-range),
    :global(.react-datepicker__day--in-range.react-datepicker__day--today),
    :global(.react-datepicker__day--in-range.react-datepicker__day--today:hover) {
        background-color: var(--apl-alias-color-primary-primary-container, #C5FFC8);
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        border-radius: 0;
        border: none;
        margin-left: 0;
        margin-right: 0;
        padding: 0;
        padding-left: 4px;
        padding-right: 4px;
    }


    :global(.react-datepicker__day--selected),
    :global(.react-datepicker__day--selected:hover) {
        background-color: var(--apl-alias-color-primary-surface-tint, #016E2E);
        color: var(--apl-alias-color-primary-on-primary, #FFF);
        border-radius: var(--apl-alias-radius-radius11, 9999px);
        text-decoration-line: none;
    }


    :global(.react-datepicker__day--keyboard-selected) {
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        border-radius: var(--apl-alias-radius-radius11, 9999px);
    }


    :global(.react-datepicker__day--in-selecting-range),
    :global(.react-datepicker__day--in-selecting-range:hover) {
        background-color: var(--apl-alias-color-primary-primary-container, #C5FFC8);
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        border-radius: 0;
    }

    :global(.react-datepicker__day--disabled) {
        color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
        background-color: transparent;
    }

    :global(.react-datepicker__day--disabled:hover) {
        background-color: transparent;
    }

    :global(.react-datepicker__day--today.react-datepicker__day--disabled) {
        outline-color: none;
        color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    }

    :global(react-datepicker__day--in-range.react-datepicker__day--disabled),
    :global(.react-datepicker__day--in-range.react-datepicker__day--disabled:hover) {
        color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
        background-color: transparent;
    }

    :global(.react-datepicker__month-text),
    :global(.react-datepicker__year-text) {
        padding: var(--apl-alias-spacing-padding-padding6, 10px);
        margin: 0;
        border-radius: var(--apl-alias-radius-radius4, 8px);
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1 0 0;
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        background-color: transparent;
    }

    :global(.react-datepicker__month-text:hover),
    :global(.react-datepicker__year-text:hover) {
        background-color: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
    }

    :global(.react-datepicker__month-text:active),
    :global(.react-datepicker__year-text:active) {
        background-color: var(--apl-alias-color-primary-primary-container, #C5FFC8);
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
    }

    :global(.react-datepicker__month-text--selected),
    :global(.react-datepicker__month-text--selected:hover),
    :global(.react-datepicker__year-text--selected),
    :global(.react-datepicker__year-text--selected:hover) {
        background-color: var(--apl-alias-color-primary-surface-tint, #016E2E);
        color: var(--apl-alias-color-primary-on-primary, #FFF);
    }


    :global(.react-datepicker__month-text--disabled),
    :global(.react-datepicker__month-text--disabled:hover),
    :global(.react-datepicker__year-text--disabled),
    :global(.react-datepicker__year-text--disabled:hover) {
        background-color: transparent;
        color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    }

    :global(.react-datepicker__day--selecting-range-end),
    :global(.react-datepicker__day--selecting-range-start),
    :global(.react-datepicker__day--selecting-range-end:hover),
    :global(.react-datepicker__day--selecting-range-start:hover),
    :global(.react-datepicker__day--range-end.react-datepicker__day--in-range),
    :global(.react-datepicker__day--range-start.react-datepicker__day--in-range) {
        background-color: var(--apl-alias-color-primary-surface-tint, #016E2E);
        color: var(--apl-alias-color-primary-on-primary, #FFF);
    }

    .inputEndDecorator {
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
    }

    .inputEndDecorator.disabled {
        color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    }


    :global(.react-datepicker__day--range-start),
    :global(.react-datepicker__day--range-start:hover) {
        border-radius: 0;
        border-top-left-radius: var(--apl-alias-spacing-radius-radius10, 24px);
        border-bottom-left-radius: var(--apl-alias-spacing-radius-radius10, 24px);
        outline: 0;
    }

    :global(.react-datepicker__day--range-end),
    :global(.react-datepicker__day--range-end:hover) {
        border-radius: 0;
        border-top-right-radius: var(--apl-alias-spacing-radius-radius10, 24px);
        border-bottom-right-radius: var(--apl-alias-spacing-radius-radius10, 24px);
        outline: 0;

    }

    :global(.react-datepicker__day--selecting-range-start),
    :global(.react-datepicker__day--selecting-range-start:hover) {
        border-top-left-radius: var(--apl-alias-spacing-radius-radius10, 24px);
        border-bottom-left-radius: var(--apl-alias-spacing-radius-radius10, 24px);
    }

    :global(.react-datepicker__day--selecting-range-end),
    :global(.react-datepicker__day--selecting-range-end:hover) {
        border-top-right-radius: var(--apl-alias-spacing-radius-radius10, 24px);
        border-bottom-right-radius: var(--apl-alias-spacing-radius-radius10, 24px);
    }

    :global(.react-datepicker__monthPicker),
    :global(.react-datepicker__year) {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--apl-alias-spacing-gap-gap5, 8px);
    }

    :global(.react-datepicker__month-wrapper) {
        width: 100%;
        display: flex;
        align-items: center;
        align-self: stretch;
        gap: var(--apl-alias-spacing-gap-gap5, 8px)
    }

    :global(.react-datepicker__year-wrapper) {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        max-width: 100%;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        justify-items: center;
        gap: var(--apl-alias-spacing-gap-gap5, 8px);
    }

    :global(.react-datepicker__time-container) {
        border-left: 0.5px solid var(--apl-alias-color-outline-and-border-outline, #ADABAB);
        padding-left: 12px;
        padding-right: 0px;
        list-style: none;
        margin: 0;
        margin-left: 12px;
        height: 100%;
        overflow-y: scroll;
        box-sizing: content-box;
        float: right;
        min-width: 85px;
    }

    :global(.react-datepicker-time__header) {
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        font-size: var(--apl-alias-typography-body-large-font-size);
        font-weight: var(--apl-alias-typography-body-large-weight-emphasized);
        text-align: center;
    }

    :global(.react-datepicker__time) {
        color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        font-size: var(--apl-alias-typography-label-large-font-size);

        :global(li.react-datepicker__time-list-item) {
            padding: var(--apl-alias-spacing-padding-padding5, 8px);
            border-radius: var(--apl-alias-radius-radius4, 8px);
            height: fit-content;
        }

        :global(li.react-datepicker__time-list-item:hover) {
            background-color: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        }

        :global(li.react-datepicker__time-list-item--selected),
        :global(li.react-datepicker__time-list-item--selected:hover) {
            background-color: var(--apl-alias-color-primary-surface-tint, #016E2E);
            color: var(--apl-alias-color-primary-on-primary, #FFF);
        }


        :global(li.react-datepicker__time-list-item--disabled),
        :global(li.react-datepicker__time-list-item--disabled:hover) {
            background-color: transparent;
            color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
        }
    }

    .dateInputDay {
        text-align: center;
        vertical-align: middle;
        padding: 5px;
    }

}

.dateInputRoot {
    position: relative;
    width: auto;
    display: inline-block;
}

.dateInputCalendarMonthYearHeader {
    display: flex;
    align-items: center;
    gap: var(--apl-alias-spacing-gap-gap3, 4px);
}

.dateInputMonth {
    text-align: center;
}

.dateInputYear {
    text-align: center;
}

:global(.react-datepicker__triangle) {
    display: none;
}

:global(.react-datepicker-popper) {
    z-index: 2;
}

:global(.react-datepicker__month-container),
:global(.react-datepicker__year--container) {
    width: 100%;
}

:global(.react-datepicker__aria-live) {
    position: absolute;
    clip-path: circle(0);
    border: 0;
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    width: 1px;
    white-space: nowrap;
}

:global(.react-datepicker.react-datepicker--time-only) {
    min-width: 150px;
}

:global(.react-datepicker--time-only .react-datepicker__time-container) {
    border-left: 0;
    width: 100%;
    padding-left: 0;
    margin-left: 0;
}

:global(.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list) {
    list-style: none;
    margin: 0;
    height: calc(195px + 1.7rem / 2);
    overflow-y: scroll;
    padding-right: 0;
    padding-left: 0;
    width: 100%;
    box-sizing: content-box;
}