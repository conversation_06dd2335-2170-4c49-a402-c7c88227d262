import { DatePickerProps as ReactDatePickerProps } from "react-datepicker"

import type { InputProps } from "../input/InputProps"

type PickInputProps = "label" | "labelDecorator" | "required" | "helperText" | "helperTextDecorator" | "error" | "placeholder" | "size" | "fullWidth"

export type DateInputEra = "ad" | "bd"
export type DateInputLocale = "en" | "th"
export type SingleDateInputValueHandler = {
  selectsRange?: never
  selectsMultiple?: never
  onChange?: (
    date: Date | null,
    event?: React.MouseEvent<HTMLElement> | React.KeyboardEvent<HTMLElement>
  ) => void
}

export type DateInputViewMode = "date" | "month" | "year"
export type DateInputProps = Omit<
  ReactDatePickerProps,
  | "placeholderText"
  | "value"
  | "selectsRange"
  | "selectsMultiple"
  | "startDate"
  | "endDate"
  | "onBlur"
> & {
  /**
   * @default 'bd'
   */
  era?: DateInputEra
  /**
   * @default 'th'
   */
  locale?: DateInputLocale
  format?: string
  inputProps?: Omit<InputProps, PickInputProps>
  value?: Date | null
  startDate?: Date | null
  endDate?: Date | null
  portal?: boolean
  onBlur?: () => void
  onViewModeChange?: (mode: DateInputViewMode) => void
  hideCalendarMonth?: boolean
  hideCalendarYear?: boolean
  shouldBackToDateViewAfterSelect?: boolean
} & Pick<InputProps, PickInputProps> &
  (
    | {
        isRange: true
        onChange?: (value: [Date | null, Date | null]) => void
      }
    | {
        isRange?: never
        onChange?: (value: Date | null) => void
      }
  )
