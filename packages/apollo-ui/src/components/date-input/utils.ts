import {
  Locale,
  format as originalFormat,
  parse as originalParse,
  setYear,
} from "date-fns"

interface FormatOptions {
  locale?: Locale
}

export function parse(
  dateString: string,
  format: string = "dd MMMM yyyy",
  parseOption: FormatOptions = {}
): Date | string {
  // Pre-process the format string to replace 'bb' with 'yy' and 'bbbb' with 'yyyy'
  let preprocessedFormatStr = format.replace(/bbbb/, "yyyy").replace(/bb/, "yy")

  const result = originalParse(
    dateString,
    preprocessedFormatStr,
    new Date(),
    parseOption
  )

  const isFullBDYear = format.includes("bbbb")
  const isBDYear = format.includes("bb")
  if (isBDYear) {
    let resultFullYear = result.getFullYear()
    if (isBDYear && !isFullBDYear) {
      const lastDigit = result
        .getFullYear()
        .toString()
        .replace(/.*(\d{2})$/g, "$1")
      const currentBDYear = new Date().getFullYear() + 543
      const currentBDYearPrefix = currentBDYear
        .toString()
        .replace(/^(\d{2}).*/g, "$1")

      resultFullYear = Number(`${currentBDYearPrefix}${lastDigit}`)
    }
    const newYear = resultFullYear - 543
    if (newYear < 1) {
      return "Invalid Date"
    }
    return setYear(result, newYear)
  }

  return result
}

export function format(
  date: Date,
  formatStr: string = "dd MMMM yyyy",
  options: FormatOptions = {}
): string {
  const buddhistEraDate = new Date(date)
  const gregorianYear = buddhistEraDate.getFullYear()
  const buddhistYear = gregorianYear + 543

  // Pre-process the format string to replace 'bb' with 'yy' and 'bbbb' with 'yyyy'
  let preprocessedFormatStr = formatStr
    .replace(/bbbb/, "[%%]yyyy[%%]")
    .replace(/bb/, "[%%]yy[%%]")

  // Use the original date-fns format function
  let formattedDate = originalFormat(buddhistEraDate, preprocessedFormatStr, {
    ...options,
    locale: options.locale,
  })

  // Post-process to replace 'yy' or 'yyyy' with Buddhist Era years
  if (formatStr.includes("bbbb")) {
    // Replace the full Buddhist Era year (4 digits)
    formattedDate = formattedDate.replace(
      /\[%%\]\d{4}\[%%\]/,
      buddhistYear.toString()
    )
  } else if (formatStr.includes("bb")) {
    // Replace the last two digits of the Buddhist Era year
    const shortBuddhistYear = buddhistYear.toString().slice(-2)
    formattedDate = formattedDate.replace(
      /\[%%\]\d{2}\[%%\]/,
      shortBuddhistYear
    )
  }

  return formattedDate
}

export type FormatMap = Record<
  string,
  {
    fullDate: string
    header: {
      year: string
      month: string
    }
  }
>

export const defaultFormatMap: FormatMap = {
  ad: {
    fullDate: "dd MMM yyyy",
    header: {
      year: "yyyy",
      month: "MMM",
    },
  },
  bd: {
    fullDate: "dd MMM bbbb",
    header: {
      year: "bbbb",
      month: "MMM",
    },
  },
}
