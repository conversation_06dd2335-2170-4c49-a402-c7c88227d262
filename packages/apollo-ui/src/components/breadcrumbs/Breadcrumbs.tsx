import {
  Children,
  isValidElement,
  useRef,
  useState,
  type ReactElement,
} from "react"
import classNames from "classnames"

import { Right } from "../common/Right"
import styles from "./breadcrumbs.module.css"
import { BreadcrumbsProps } from "./BreadcrumbsProps"

const insertSeparators = (items: ReactElement[], separator?: React.ReactNode) =>
  items.flatMap((item, index) =>
    index < items.length - 1
      ? [
          item,
          <li
            key={item.key ? `separator-${item.key}` : `separator-${index}`}
            aria-hidden
            className={classNames(styles.breadcrumbsSeparator)}
          >
            {separator || <Right />}
          </li>,
        ]
      : [item]
  )

export const Breadcrumbs = ({
  children,
  separator,
  itemsAfterCollapse = 1,
  itemsBeforeCollapse = 1,
  maxItems = 7,
  ref,
  className,
  ...navProps
}: BreadcrumbsProps) => {
  const [expanded, setExpanded] = useState(false)
  const listRef = useRef<HTMLOListElement>(null)
  const allChildren = Children.toArray(children).filter(isValidElement)

  const handleExpand = () => {
    setExpanded(true)
    const focusable = listRef.current?.querySelector<HTMLElement>(
      "a[href],button,[tabindex]"
    )
    focusable?.focus()
  }

  const renderCollapsedItems = (items: ReactElement[]) => {
    if (itemsBeforeCollapse + itemsAfterCollapse >= items.length) {
      console.error(
        `ERROR: Invalid props — itemsAfterCollapse (${itemsAfterCollapse}) + itemsBeforeCollapse (${itemsBeforeCollapse}) >= maxItems (${maxItems})`
      )
      return items
    }

    return [
      ...items.slice(0, itemsBeforeCollapse),
      <li
        key="ellipsis"
        onClick={handleExpand}
        className={classNames(
          "ApolloBreadcrumbs-ellipsis",
          styles.breadcrumbsEllipsis
        )}
      >
        ...
      </li>,
      ...items.slice(-itemsAfterCollapse),
    ]
  }

  const items = allChildren.map((child, index) => {
    const disabledItem =
      isValidElement<{ "aria-disabled": boolean; disabled: boolean }>(child) &&
      (child.props["aria-disabled"] || child.props.disabled)
    return (
      <li
        key={child.key || `child-${index}`}
        className={classNames(
          "ApolloBreadcrumbs-item",
          styles.breadcrumbsItem,
          {
            [styles.breadcrumbsLastItem]: index === allChildren.length - 1,
            [styles.breadcrumbsDisabled]: disabledItem,
          }
        )}
      >
        {child}
      </li>
    )
  })

  const visibleItems =
    expanded || items.length <= maxItems ? items : renderCollapsedItems(items)

  return (
    <nav
      {...navProps}
      ref={ref}
      className={classNames(
        "ApolloBreadcrumbs-root",
        styles.breadcrumbsRoot,
        className
      )}
    >
      <ol
        ref={listRef}
        className={classNames("ApolloBreadcrumbs-list", styles.breadcrumbsList)}
      >
        {insertSeparators(visibleItems, separator)}
      </ol>
    </nav>
  )
}
