.breadcrumbsList {
    display: flex;
    align-items: center;
    align-content: center;
    gap: 8px var(--apl-alias-spacing-gap-gap5, 8px);
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
}

.breadcrumbsRoot {
    composes: apl-typography-body-small from "../../base.module.css";

    & :global(.ApolloTypography--root) {
        font-size: inherit;
    }

    & button,
    & a {
        font-size: inherit;
        padding: 0;
        height: inherit;
        min-height: inherit;
    }

    & svg {
        width: 14px;
    }
}

.breadcrumbsItem {
    display: flex;
    padding: 4px;
    align-items: flex-start;
    gap: 10px;
    color: var(--apl-alias-color-background-and-surface-on-surface, #474647);

    &:hover {
        border-radius: 4px;
        background-color: var(--apl-alias-color-primary-text-only-background-hovered, #E5E2E2);
    }

    & a {
        color: inherit;
        text-decoration: none;
    }

    & button,
    & a {
        color: inherit;

        &:enabled,
        &[href] {
            text-decoration: none;

            &:hover {
                color: inherit;
            }

            &:focus-visible {
                color: inherit;
            }
        }
    }
}

.breadcrumbsEllipsis {
    display: flex;
    padding: 4px;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
    color: var(--apl-alias-color-primary-primary, #016E2E);
}

.breadcrumbsSeparator {
    display: flex;
    align-items: center;
    color: var(--apl-alias-color-background-and-surface-on-surface-variant, #C8C6C6);

    & svg {
        width: 12px;
    }
}

.breadcrumbsLastItem {
    color: var(--apl-alias-color-primary-primary, #016E2E);
}

.breadcrumbsDisabled {
    color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    cursor: not-allowed;
    pointer-events: none;
}