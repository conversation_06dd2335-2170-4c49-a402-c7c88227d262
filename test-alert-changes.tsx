import React from 'react';
import { Alert } from './packages/apollo-ui/src/components/alert';

// Test that the new type prop works with all values
function TestAlert() {
  return (
    <div>
      <Alert type="success" title="Success" description="Success message" />
      <Alert type="information" title="Information" description="Information message" />
      <Alert type="warning" title="Warning" description="Warning message" />
      <Alert type="error" title="Error" description="Error message" />
      
      {/* Test default value */}
      <Alert title="Default" description="Should default to information" />
    </div>
  );
}

export default TestAlert;
