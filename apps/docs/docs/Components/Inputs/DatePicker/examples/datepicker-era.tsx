import { useState } from "react"
import { DateInput } from "@apollo/ui"

export default function DatePickerEra() {
  const [dateBuddhistEra, setDateBuddhistEra] = useState<Date | null>(null)
  const [dateGregorianEra, setDateGregorianEra] = useState<Date | null>(null)

  return (
    <div style={styles.container}>
      <div style={styles.row}>
        <DateInput
          label="Buddhist Era (default)"
          placeholder="Select date"
          era="bd"
          value={dateBuddhistEra}
          onChange={setDateBuddhistEra}
        />

        <DateInput
          label="Gregorian Era (AD)"
          placeholder="Select date"
          era="ad"
          value={dateGregorianEra}
          onChange={setDateGregorianEra}
        />
      </div>
    </div>
  )
}

const styles = {
  container: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    width: "100%",
  },
  row: {
    display: "flex",
    flexDirection: "row",
    gap: "24px",
    flexWrap: "wrap",
  },
} as any
