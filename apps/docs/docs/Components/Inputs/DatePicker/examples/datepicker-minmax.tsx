import { useState } from "react"
import { DateInput } from "@apollo/ui"

export default function DatePickerMinMax() {
  const [date, setDate] = useState<Date | null>(null)

  // Set min date to 5 days before today and max date to 5 days after today
  const minDate = new Date(new Date().setDate(new Date().getDate() - 5))
  const maxDate = new Date(new Date().setDate(new Date().getDate() + 5))

  return (
    <div style={styles}>
      <DateInput
        label="Limited date range"
        placeholder="Select date"
        value={date}
        onChange={setDate}
        minDate={minDate}
        maxDate={maxDate}
        helperText="Can only select dates ±5 days from today"
      />
    </div>
  )
}

const styles = {
  display: "flex",
  flexDirection: "column",
  gap: "16px",
  width: "100%",
  maxWidth: "320px",
} as any
