import { useState } from "react"
import { DateInput } from "@apollo/ui"

export default function DatePickerViewMode() {
  const [dateMonthView, setDateMonthView] = useState<Date | null>(null)
  const [dateYearView, setDateYearView] = useState<Date | null>(null)

  return (
    <div style={styles.container}>
      <div style={styles.row}>
        <DateInput
          label="Month picker"
          placeholder="Select month"
          showMonthYearPicker
          value={dateMonthView}
          onChange={setDateMonthView}
        />

        <DateInput
          label="Year picker"
          placeholder="Select year"
          showYearPicker
          value={dateYearView}
          onChange={setDateYearView}
        />
      </div>
    </div>
  )
}

const styles = {
  container: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    width: "100%",
  },
  row: {
    display: "flex",
    flexDirection: "row",
    gap: "24px",
    flexWrap: "wrap",
  },
} as any
