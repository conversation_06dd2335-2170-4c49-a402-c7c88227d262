import { useState } from "react"
import { DateInput } from "@apollo/ui"

export default function DatePickerRange() {
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ])

  return (
    <div style={styles}>
      <DateInput
        isRange
        label="Date range"
        placeholder="Select date range"
        startDate={dateRange[0]}
        endDate={dateRange[1]}
        onChange={setDateRange}
      />
    </div>
  )
}

const styles = {
  display: "flex",
  flexDirection: "column",
  gap: "16px",
  width: "100%",
  maxWidth: "320px",
} as any
