import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@apollo/ui"
import { CheckCircle, InfoCircle } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

export const Alerts = () => (
  <>
    <ComponentGroup>
      <ComponentBox>
        <Alert
          fullWidth
          title="Info Alert"
          description="This is an informational alert with default color"
        />
      </ComponentBox>
      <ComponentBox>
        <Alert
          fullWidth
          type="success"
          title="Success Alert"
          description="This is a success alert"
        />
      </ComponentBox>
      <ComponentBox>
        <Alert
          fullWidth
          type="warning"
          title="Warning Alert"
          description="This is a warning alert"
        />
      </ComponentBox>
      <ComponentBox>
        <Alert
          fullWidth
          type="error"
          title="Error Alert"
          description="This is an error alert"
        />
      </ComponentBox>
    </ComponentGroup>

    <ComponentGroup>
      <ComponentBox>
        <Alert
          fullWidth
          title="Alert with custom start decorator"
          description="This alert has a custom icon"
          startDecorator={<InfoCircle size={16} color="blue" />}
        />
      </ComponentBox>
      <ComponentBox>
        <Alert
          fullWidth
          title="Alert with end decorator"
          description="This alert has a custom end element"
          endDecorator={
            <IconButton size="small">
              <CheckCircle size={18} />
            </IconButton>
          }
        />
      </ComponentBox>
      <ComponentBox>
        <Alert
          fullWidth
          title="Closable Alert"
          description="This alert has a close button"
          onClose={() => alert("Alert closed!")}
          action={<Button variant="outline" size="small">Action</Button>}
        />
      </ComponentBox>
    </ComponentGroup>

    <ComponentGroup>
      <Alert
        fullWidth
        title="Full Width Alert"
        description="This alert spans the full width of its container"
      />
    </ComponentGroup>

    <ComponentGroup>
      <ComponentBox>
        <Alert fullWidth title="Alert with only title" />
      </ComponentBox>
      <ComponentBox>
        <Alert description="Alert with only description" />
      </ComponentBox>
    </ComponentGroup>
  </>
)
