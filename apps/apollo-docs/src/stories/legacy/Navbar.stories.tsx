import { useState } from "react"
import {
  NavB<PERSON>,
  createTheme,
  ThemeProvider,
  Typo<PERSON>,
} from "@apollo/ui/legacy"
import {
  Home,
  Shop,
  Shopping,
  Solution,
  User,
  Bell,
  Search,
  Heart,
} from "@design-systems/apollo-icons"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Navigation/NavBar",
  component: NavBar,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "NavBar provides a horizontal navigation component with menu items and icons. It supports active states, responsive behavior, and customizable styling options.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    menu: {
      control: { type: "object" },
      description: "Array of menu items with label and icon",
      table: {
        type: { summary: "NavBarMenuItem[]" },
      },
    },
    activeIndex: {
      control: { type: "number" },
      description: "Index of the active menu item",
      table: {
        type: { summary: "number" },
      },
    },
    hideShadow: {
      control: { type: "boolean" },
      description: "Whether to hide the bottom shadow",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    onChange: {
      control: false,
      description: "Callback function when menu item is selected",
      table: {
        type: { summary: "(index: number) => void" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names",
      table: {
        type: { summary: "string" },
      },
    },
  },
} satisfies Meta<typeof NavBar>

export default meta
type Story = StoryObj<typeof meta>

// Basic NavBar
export const Basic: Story = {
  args: {
    menu: [
      { label: "Home", icon: <Home /> },
      { label: "Shop", icon: <Shop /> },
      { label: "Orders", icon: <Shopping /> },
      { label: "Profile", icon: <User /> },
    ],
  },
  render: function BasicComponent() {
    const [activeIndex, setActiveIndex] = useState(0)

    return (
      <NavBar
        menu={[
          { label: "Home", icon: <Home /> },
          { label: "Shop", icon: <Shop /> },
          { label: "Orders", icon: <Shopping /> },
          { label: "Profile", icon: <User /> },
        ]}
        activeIndex={activeIndex}
        onChange={setActiveIndex}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Basic NavBar with four menu items. Click items to see active state changes.",
      },
    },
  },
}

// Without shadow
export const NoShadow: Story = {
  args: {
    menu: [
      { label: "Home", icon: <Home /> },
      { label: "Shop", icon: <Shop /> },
      { label: "Orders", icon: <Shopping /> },
      { label: "Profile", icon: <User /> },
    ],
  },
  render: function NoShadowComponent() {
    const [activeIndex, setActiveIndex] = useState(1)

    return (
      <NavBar
        hideShadow
        menu={[
          { label: "Home", icon: <Home /> },
          { label: "Shop", icon: <Shop /> },
          { label: "Orders", icon: <Shopping /> },
          { label: "Profile", icon: <User /> },
        ]}
        activeIndex={activeIndex}
        onChange={setActiveIndex}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "NavBar without bottom shadow for cleaner integration.",
      },
    },
  },
}

// Many items
export const ManyItems: Story = {
  args: {
    menu: [
      { label: "Home", icon: <Home /> },
      { label: "Shop", icon: <Shop /> },
      { label: "Orders", icon: <Shopping /> },
      { label: "Solutions", icon: <Solution /> },
      { label: "Favorites", icon: <Heart /> },
      { label: "Notifications", icon: <Bell /> },
      { label: "Search", icon: <Search /> },
      { label: "Profile", icon: <User /> },
    ],
  },
  render: function ManyItemsComponent() {
    const [activeIndex, setActiveIndex] = useState(2)

    return (
      <NavBar
        menu={[
          { label: "Home", icon: <Home /> },
          { label: "Shop", icon: <Shop /> },
          { label: "Orders", icon: <Shopping /> },
          { label: "Solutions", icon: <Solution /> },
          { label: "Favorites", icon: <Heart /> },
          { label: "Notifications", icon: <Bell /> },
          { label: "Search", icon: <Search /> },
          { label: "Profile", icon: <User /> },
        ]}
        activeIndex={activeIndex}
        onChange={setActiveIndex}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "NavBar with many items demonstrating horizontal scrolling behavior.",
      },
    },
  },
}

// Minimal items
export const MinimalItems: Story = {
  args: {
    menu: [
      { label: "Home", icon: <Home /> },
      { label: "Profile", icon: <User /> },
    ],
  },
  render: function MinimalItemsComponent() {
    const [activeIndex, setActiveIndex] = useState(0)

    return (
      <NavBar
        menu={[
          { label: "Home", icon: <Home /> },
          { label: "Profile", icon: <User /> },
        ]}
        activeIndex={activeIndex}
        onChange={setActiveIndex}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "NavBar with minimal items for simple navigation.",
      },
    },
  },
}

// Icons only (short labels)
export const IconsOnly: Story = {
  args: {
    menu: [
      { label: "🏠", icon: <Home /> },
      { label: "🛍️", icon: <Shop /> },
      { label: "📦", icon: <Shopping /> },
      { label: "👤", icon: <User /> },
    ],
  },
  render: function IconsOnlyComponent() {
    const [activeIndex, setActiveIndex] = useState(1)

    return (
      <NavBar
        menu={[
          { label: "🏠", icon: <Home /> },
          { label: "🛍️", icon: <Shop /> },
          { label: "📦", icon: <Shopping /> },
          { label: "👤", icon: <User /> },
        ]}
        activeIndex={activeIndex}
        onChange={setActiveIndex}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "NavBar with emoji labels for a more compact appearance.",
      },
    },
  },
}

// Controlled state with content
export const WithContent: Story = {
  args: {
    menu: [
      { label: "Home", icon: <Home /> },
      { label: "Shop", icon: <Shop /> },
      { label: "Orders", icon: <Shopping /> },
      { label: "Profile", icon: <User /> },
    ],
  },
  render: function WithContentComponent() {
    const [activeIndex, setActiveIndex] = useState(0)

    const menuItems = [
      { label: "Home", icon: <Home /> },
      { label: "Shop", icon: <Shop /> },
      { label: "Orders", icon: <Shopping /> },
      { label: "Profile", icon: <User /> },
    ]

    const content = [
      "Welcome to the Home page! Here you can find an overview of all features.",
      "Browse our Shop with thousands of products available for purchase.",
      "View your Orders history and track current shipments.",
      "Manage your Profile settings and personal information.",
    ]

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "24px", width: "100%" }}>
        <NavBar
          menu={menuItems}
          activeIndex={activeIndex}
          onChange={setActiveIndex}
        />
        <div
          style={{
            padding: "24px",
            backgroundColor: "#f8f9fa",
            borderRadius: "8px",
            border: "1px solid #e9ecef",
            minHeight: "120px",
            textAlign: "center",
          }}
        >
          <Typography level="h4" style={{ marginBottom: "12px" }}>
            {menuItems[activeIndex].label}
          </Typography>
          <Typography level="body-1" style={{ color: "#6c757d" }}>
            {content[activeIndex]}
          </Typography>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "NavBar integrated with content that changes based on the selected menu item.",
      },
    },
  },
}

// Custom styling
export const CustomStyling: Story = {
  args: {
    menu: [
      { label: "Dashboard", icon: <Home /> },
      { label: "Analytics", icon: <Solution /> },
      { label: "Settings", icon: <User /> },
    ],
  },
  render: function CustomStylingComponent() {
    const [activeIndex, setActiveIndex] = useState(0)

    return (
      <div style={{ backgroundColor: "#1f2937", padding: "20px", borderRadius: "8px" }}>
        <NavBar
          className="custom-navbar"
          menu={[
            { label: "Dashboard", icon: <Home /> },
            { label: "Analytics", icon: <Solution /> },
            { label: "Settings", icon: <User /> },
          ]}
          activeIndex={activeIndex}
          onChange={setActiveIndex}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "NavBar with custom styling and dark background theme.",
      },
    },
  },
}
