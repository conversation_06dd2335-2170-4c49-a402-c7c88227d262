import React from "react"
import { Typography, createTheme, ThemeProvider } from "@apollo/ui/legacy"
import { History, FileText, Setting, Bulb, Safety, Mobile } from "@design-systems/apollo-icons"
import ResourceCard from "../../components/resource-card/ResourceCard"
import { Meta } from "@storybook/addon-docs/blocks"

<Meta title="@design-systems∕apollo-ui/Introduction" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="h1"
    style={{ margin: "0px", color: "#121212" }}
  >@design-systems/apollo-ui</Typography>
  <Typography
    align="center"
    level="h4"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Legacy React component library - Apollo Design System v1</Typography>
  <div
    style={{
      display: "flex",
      gap: 8,
      flexWrap: "wrap",
      justifyContent: "center",
      padding: "32px",
      minWidth: "460px",
    }}
  >
    <History size={64} style={{ color: "#0154EE" }} />
  </div>
</div>

## What is @design-systems/apollo-ui?

<ThemeProvider theme={createTheme()}>
  <Typography
    level="body-1"
    style={{
      color: "var(--sb-secondary-text-color)",
      marginBottom: 32,
    }}
  >
    @design-systems/apollo-ui is the legacy version of the Apollo Design System component library. This package represents Apollo v1 and is maintained for backward compatibility and existing projects. While still functional and supported, new projects should consider migrating to the modern <code style={{ background: "rgb(247, 250, 252)", color: "rgba(46, 52, 56, 0.9)", padding: "3px 5px", borderRadius: 3 }}>@apollo/ui</code> package for enhanced features and improved developer experience.
  </Typography>
</ThemeProvider>

## Legacy Features

<ThemeProvider theme={createTheme()}>
  <div
    style={{
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))",
      gap: 24,
      marginBottom: 32,
    }}
  >
    <div style={{ padding: "20px", border: "1px solid var(--sb-ui-border-color)", borderRadius: "12px" }}>
      <div style={{ display: "flex", alignItems: "center", gap: "12px", marginBottom: "12px" }}>
        <Setting size={24} style={{ color: "#0154EE" }} />
        <Typography level="h5" style={{ margin: 0 }}>Tailwind Integration</Typography>
      </div>
      <Typography level="body-2" style={{ color: "var(--sb-secondary-text-color)" }}>
        Built with TailwindCSS utilities and custom design tokens for consistent styling across components.
      </Typography>
    </div>

    <div style={{ padding: "20px", border: "1px solid var(--sb-ui-border-color)", borderRadius: "12px" }}>
      <div style={{ display: "flex", alignItems: "center", gap: "12px", marginBottom: "12px" }}>
        <Safety size={24} style={{ color: "#0154EE" }} />
        <Typography level="h5" style={{ margin: 0 }}>TypeScript Support</Typography>
      </div>
      <Typography level="body-2" style={{ color: "var(--sb-secondary-text-color)" }}>
        Comprehensive TypeScript definitions for enhanced developer experience and type safety.
      </Typography>
    </div>

    <div style={{ padding: "20px", border: "1px solid var(--sb-ui-border-color)", borderRadius: "12px" }}>
      <div style={{ display: "flex", alignItems: "center", gap: "12px", marginBottom: "12px" }}>
        <Mobile size={24} style={{ color: "#0154EE" }} />
        <Typography level="h5" style={{ margin: 0 }}>Responsive Design</Typography>
      </div>
      <Typography level="body-2" style={{ color: "var(--sb-secondary-text-color)" }}>
        Mobile-first responsive components that work seamlessly across all device sizes.
      </Typography>
    </div>
  </div>
</ThemeProvider>

## Component Categories

The legacy design system organizes components into logical categories to help you find what you need:

<div style={{ marginBottom: 32 }}>
  <div style={{
    display: "grid",
    gap: 16,
    gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))"
  }}>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Inputs
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Button, IconButton, FloatButton, Input, Checkbox, Radio, Select, Switch, DateInput, Autocomplete, UploadBox
      </Typography>
    </div>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Data Display
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Typography, Chip, SortingIcon, ProductCard and components for presenting information
      </Typography>
    </div>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Navigation
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Breadcrumbs, Pagination, NavBar, Sidebar
      </Typography>
    </div>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Feedback
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Modal, Alert, Toast
      </Typography>
    </div>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Layout
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Accordion, Tabs, CapsuleTab
      </Typography>
    </div>
  </div>
</div>

## Migration Notice

<div
  style={{
    background: "linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)",
    borderRadius: "12px",
    padding: "20px",
    margin: "16px 0",
    borderLeft: "4px solid #f59e0b"
  }}
>
  <h4 style={{
    margin: "0 0 12px 0",
    color: "#92400e",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>⚠️</span> Legacy Package Notice
  </h4>

  <p style={{ margin: "0 0 12px 0", color: "#92400e" }}>
    This is the legacy version of Apollo Design System. For new projects, we recommend using <code style={{
      background: "rgba(245, 158, 11, 0.2)",
      padding: "2px 6px",
      borderRadius: "3px"
    }}>@apollo/ui</code> which offers:
  </p>

  <ul style={{ margin: "0", paddingLeft: "20px", color: "#92400e" }}>
    <li>Modern token-based theming system</li>
    <li>Enhanced accessibility features</li>
    <li>Improved performance and bundle size</li>
    <li>Better TypeScript support</li>
    <li>Active development and new features</li>
  </ul>
</div>

## Getting Started

Ready to work with the legacy design system? Check out our comprehensive guides:

<div
  style={{
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(260px, 1fr))",
    gap: 16,
    marginBottom: 32,
  }}
>
  <ResourceCard
    page="design-systems∕apollo-ui-quick-start"
    title="Quick Start"
    description="Get up and running with the legacy components"
    icon={<Bulb size={24} />}
  />
  <ResourceCard
    page="design-systems∕apollo-ui-catalog"
    title="Component Catalog"
    description="Browse all available legacy components"
    icon={<History size={24} />}
  />
</div>

## Support and Maintenance

<ThemeProvider theme={createTheme()}>
  <Typography level="body-1" style={{ color: "var(--sb-secondary-text-color)", marginBottom: 16 }}>
    While @design-systems/apollo-ui is in maintenance mode, it continues to receive:
  </Typography>

  <ul style={{ color: "var(--sb-secondary-text-color)", marginBottom: 32 }}>
    <li><strong>Bug fixes</strong> for critical issues</li>
    <li><strong>Security updates</strong> for dependencies</li>
    <li><strong>Documentation maintenance</strong> and examples</li>
    <li><strong>Migration support</strong> to help transition to @apollo/ui</li>
  </ul>

  <Typography level="body-1" style={{ color: "var(--sb-secondary-text-color)" }}>
    For questions, issues, or migration assistance, please reach out to the Apollo Design System team.
  </Typography>
</ThemeProvider>
