import React from "react"
import { createTheme, ThemeProvider, Typo<PERSON> } from "@apollo/ui/legacy"
import { Meta } from "@storybook/addon-docs/blocks"

import Catalog from "../../components/catalog/Catalog"
import legacyComponents from "../../components/catalog/legacyComponents"

import "../../app/tailwind.css"

<Meta title="@design-systems∕apollo-ui/Catalog" tags={["docs"]} />

<ThemeProvider theme={createTheme()}>
  <Typography level="h1" style={{ marginBottom: "16px", color: "#121212" }}>
    Catalog
  </Typography>
  <Catalog components={legacyComponents} />
</ThemeProvider>
