import { useState } from "react"
import {
  CapsuleTab,
  createTheme,
  ThemeProvider,
  Typo<PERSON>,
} from "@apollo/ui/legacy"
import type { Meta, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Layout/CapsuleTab",
  component: CapsuleTab,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "CapsuleTab provides a pill-style tabbed interface for compact navigation. It allows users to switch between different views or content sections with a modern capsule design.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    tabs: {
      control: { type: "object" },
      description: "Array of tab objects with id and label properties",
      table: {
        type: { summary: "CapsuleTabItem[]" },
      },
    },
    selectedIndex: {
      control: { type: "number" },
      description: "Index of the currently selected tab (0-based)",
      table: {
        type: { summary: "number" },
      },
    },
    onSelect: {
      control: false,
      description: "Callback function when a tab is selected",
      table: {
        type: { summary: "(index: number) => void" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names",
      table: {
        type: { summary: "string" },
      },
    },
  },
} satisfies Meta<typeof CapsuleTab>

export default meta
type Story = StoryObj<typeof meta>

// Basic CapsuleTab
export const Basic: Story = {
  args: {
    tabs: [
      { id: "tab1", label: "Overview" },
      { id: "tab2", label: "Details" },
      { id: "tab3", label: "Settings" },
    ],
  },
  render: function BasicComponent() {
    const [selectedIndex, setSelectedIndex] = useState(0)

    return (
      <CapsuleTab
        tabs={[
          { id: "tab1", label: "Overview" },
          { id: "tab2", label: "Details" },
          { id: "tab3", label: "Settings" },
        ]}
        selectedIndex={selectedIndex}
        onSelect={setSelectedIndex}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Basic CapsuleTab with three tabs. Click tabs to see selection behavior.",
      },
    },
  },
}

// Two tabs
export const TwoTabs: Story = {
  args: {
    tabs: [
      { id: "active", label: "Active" },
      { id: "inactive", label: "Inactive" },
    ],
  },
  render: function TwoTabsComponent() {
    const [selectedIndex, setSelectedIndex] = useState(0)

    return (
      <CapsuleTab
        tabs={[
          { id: "active", label: "Active" },
          { id: "inactive", label: "Inactive" },
        ]}
        selectedIndex={selectedIndex}
        onSelect={setSelectedIndex}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "CapsuleTab with two options, commonly used for binary choices.",
      },
    },
  },
}

// Many tabs
export const ManyTabs: Story = {
  args: {
    tabs: [
      { id: "home", label: "Home" },
      { id: "products", label: "Products" },
      { id: "services", label: "Services" },
      { id: "about", label: "About" },
      { id: "contact", label: "Contact" },
    ],
  },
  render: function ManyTabsComponent() {
    const [selectedIndex, setSelectedIndex] = useState(0)

    return (
      <CapsuleTab
        tabs={[
          { id: "home", label: "Home" },
          { id: "products", label: "Products" },
          { id: "services", label: "Services" },
          { id: "about", label: "About" },
          { id: "contact", label: "Contact" },
        ]}
        selectedIndex={selectedIndex}
        onSelect={setSelectedIndex}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "CapsuleTab with multiple tabs for navigation menus.",
      },
    },
  },
}

// Long labels
export const LongLabels: Story = {
  args: {
    tabs: [
      { id: "dashboard", label: "Dashboard Overview" },
      { id: "analytics", label: "Analytics & Reports" },
      { id: "settings", label: "Account Settings" },
    ],
  },
  render: function LongLabelsComponent() {
    const [selectedIndex, setSelectedIndex] = useState(0)

    return (
      <CapsuleTab
        tabs={[
          { id: "dashboard", label: "Dashboard Overview" },
          { id: "analytics", label: "Analytics & Reports" },
          { id: "settings", label: "Account Settings" },
        ]}
        selectedIndex={selectedIndex}
        onSelect={setSelectedIndex}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "CapsuleTab with longer descriptive labels.",
      },
    },
  },
}

// Controlled state example
export const ControlledState: Story = {
  args: {
    tabs: [
      { id: "profile", label: "Profile" },
      { id: "preferences", label: "Preferences" },
      { id: "notifications", label: "Notifications" },
    ],
  },
  render: function ControlledStateComponent() {
    const [selectedIndex, setSelectedIndex] = useState(1)

    const handleTabChange = (index: number) => {
      setSelectedIndex(index)
    }

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px", alignItems: "center" }}>
        <CapsuleTab
          tabs={[
            { id: "profile", label: "Profile" },
            { id: "preferences", label: "Preferences" },
            { id: "notifications", label: "Notifications" },
          ]}
          selectedIndex={selectedIndex}
          onSelect={handleTabChange}
        />
        <Typography level="body-2" style={{ color: "#6b7280" }}>
          Current selection: {["Profile", "Preferences", "Notifications"][selectedIndex]} (Index: {selectedIndex})
        </Typography>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Example showing controlled state management with current selection display.",
      },
    },
  },
}

// With content panels
export const WithContentPanels: Story = {
  args: {
    tabs: [
      { id: "overview", label: "Overview" },
      { id: "details", label: "Details" },
      { id: "settings", label: "Settings" },
    ],
  },
  render: function WithContentPanelsComponent() {
    const [selectedIndex, setSelectedIndex] = useState(0)

    const content = [
      "This is the content for the first tab. It contains information about the overview section.",
      "This is the content for the second tab. Here you can find detailed information and specifications.",
      "This is the content for the third tab. Configure your settings and preferences here.",
    ]

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "24px", width: "400px" }}>
        <CapsuleTab
          tabs={[
            { id: "overview", label: "Overview" },
            { id: "details", label: "Details" },
            { id: "settings", label: "Settings" },
          ]}
          selectedIndex={selectedIndex}
          onSelect={setSelectedIndex}
        />
        <div
          style={{
            padding: "20px",
            backgroundColor: "#f8f9fa",
            borderRadius: "8px",
            border: "1px solid #e9ecef",
            minHeight: "120px",
          }}
        >
          <Typography level="body-1">{content[selectedIndex]}</Typography>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "CapsuleTab integrated with content panels that change based on selection.",
      },
    },
  },
}

// Single tab (edge case)
export const SingleTab: Story = {
  args: {
    tabs: [{ id: "only", label: "Only Option" }],
  },
  render: () => {
    return (
      <CapsuleTab
        tabs={[
          { id: "only", label: "Only Option" },
        ]}
        selectedIndex={0}
        onSelect={() => {}}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "CapsuleTab with only one tab (edge case scenario).",
      },
    },
  },
}

// Custom styling
export const CustomStyling: Story = {
  args: {
    tabs: [
      { id: "light", label: "Light Mode" },
      { id: "dark", label: "Dark Mode" },
      { id: "auto", label: "Auto" },
    ],
  },
  render: function CustomStylingComponent() {
    const [selectedIndex, setSelectedIndex] = useState(1)

    return (
      <div style={{ backgroundColor: "#1f2937", padding: "20px", borderRadius: "8px" }}>
        <CapsuleTab
          className="custom-capsule-tab"
          tabs={[
            { id: "light", label: "Light Mode" },
            { id: "dark", label: "Dark Mode" },
            { id: "auto", label: "Auto" },
          ]}
          selectedIndex={selectedIndex}
          onSelect={setSelectedIndex}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "CapsuleTab with custom styling and dark background theme.",
      },
    },
  },
}
