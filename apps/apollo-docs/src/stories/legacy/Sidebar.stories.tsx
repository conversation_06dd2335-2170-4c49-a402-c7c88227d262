import { useState } from "react"
import {
  <PERSON><PERSON>,
  createTheme,
  ThemeProvider,
  Typography,
  MenuItem,
  MenuItemGroup,
} from "@apollo/ui/legacy"
import {
  Home,
  Shop,
  Shopping,
  Solution,
  User,
  Setting,
} from "@design-systems/apollo-icons"
import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Navigation/Sidebar",
  component: Sidebar,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px", height: "600px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "Sidebar provides a vertical navigation component with collapsible menu items, headers, and footer sections. It supports nested menus, selection states, and responsive behavior.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    title: {
      control: { type: "text" },
      description: "Title displayed in the sidebar header",
      table: {
        type: { summary: "string" },
      },
    },
    menus: {
      control: { type: "object" },
      description: "Array of menu items and groups",
      table: {
        type: { summary: "SidebarMenu[]" },
      },
    },
    selectedMenuKey: {
      control: { type: "text" },
      description: "Key of the currently selected menu item",
      table: {
        type: { summary: "string" },
      },
    },
    collapsible: {
      control: { type: "boolean" },
      description: "Whether the sidebar can be collapsed",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    collapsed: {
      control: { type: "boolean" },
      description: "Whether the sidebar is collapsed",
      table: {
        type: { summary: "boolean" },
      },
    },
    width: {
      control: { type: "text" },
      description: "Width of the sidebar when expanded",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "240px" },
      },
    },
    onSelectMenu: {
      control: false,
      description: "Callback when menu item is selected",
      table: {
        type: { summary: "(key: string) => void" },
      },
    },
    onLogOut: {
      control: false,
      description: "Callback for logout action",
      table: {
        type: { summary: "() => void" },
      },
    },
  },
} satisfies Meta<typeof Sidebar>

export default meta
type Story = StoryObj<typeof meta>

const basicMenus = [
  {
    key: "home",
    icon: <Home />,
    label: "Home",
  },
  {
    key: "shop",
    icon: <Shop />,
    label: "Shop",
  },
  {
    key: "orders",
    icon: <Shopping />,
    label: "Orders",
  },
  {
    key: "profile",
    icon: <User />,
    label: "Profile",
  },
]

const complexMenus = [
  {
    key: "dashboard",
    icon: <Home />,
    label: "Dashboard",
  },
  {
    key: "ecommerce",
    icon: <Shop />,
    label: "E-commerce",
    children: [
      { key: "products", label: "Products" },
      { key: "orders", label: "Orders" },
      { key: "customers", label: "Customers" },
    ],
  },
  {
    key: "analytics",
    icon: <Solution />,
    label: "Analytics",
    children: [
      { key: "reports", label: "Reports" },
      { key: "insights", label: "Insights" },
    ],
  },
  {
    key: "settings",
    icon: <Setting />,
    label: "Settings",
    children: [
      { key: "general", label: "General" },
      { key: "security", label: "Security" },
      { key: "notifications", label: "Notifications" },
    ],
  },
]

// Basic Sidebar
export const Basic: Story = {
  args: {
    menus: basicMenus,
  },
  render: function BasicComponent() {
    const [selectedMenu, setSelectedMenu] = useState("home")

    return (
      <div style={{ height: "500px", border: "1px solid #e9ecef", borderRadius: "8px", background: "#f8f9fa" }}>
        <Sidebar
          title="Apollo"
          menus={basicMenus}
          selectedMenuKey={selectedMenu}
          onSelectMenu={(key) => setSelectedMenu(String(key))}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Basic sidebar with simple menu items and selection state.",
      },
    },
  },
}

// Collapsible Sidebar
export const Collapsible: Story = {
  args: {
    menus: basicMenus,
  },
  render: function CollapsibleComponent() {
    const [selectedMenu, setSelectedMenu] = useState("home")
    const [collapsed, setCollapsed] = useState(false)

    return (
      <div style={{ background: "#f8f9fa", height: "500px", border: "1px solid #e9ecef", borderRadius: "8px", transition: "width 0.3s ease" }}>
        <Sidebar
          title="Apollo"
          collapsible
          collapsed={collapsed}
          onCollapsedChange={setCollapsed}
          menus={basicMenus}
          selectedMenuKey={selectedMenu}
          onSelectMenu={(key) => setSelectedMenu(String(key))}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Collapsible sidebar that can be toggled between expanded and collapsed states.",
      },
    },
  },
}

// With nested menus
export const WithNestedMenus: Story = {
  args: {
    menus: complexMenus,
  },
  render: function WithNestedMenusComponent() {
    const [selectedMenu, setSelectedMenu] = useState("dashboard")
    const [expandedMenuKeys, setExpandedMenuKeys] = useState<string[]>(["ecommerce"])

    return (
      <div style={{ background: "#f8f9fa", height: "500px", border: "1px solid #e9ecef", borderRadius: "8px" }}>
        <Sidebar
          title="Admin Panel"
          menus={complexMenus}
          selectedMenuKey={selectedMenu}
          onSelectMenu={(key) => setSelectedMenu(String(key))}
          expandedMenuKeys={expandedMenuKeys}
          onExpandedChange={(key, expanded) => {
            if (expanded) {
              setExpandedMenuKeys([...expandedMenuKeys, String(key)])
            } else {
              setExpandedMenuKeys(expandedMenuKeys.filter(k => k !== String(key)))
            }
          }}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Sidebar with nested menu groups that can be expanded and collapsed.",
      },
    },
  },
}

// With footer
export const WithFooter: Story = {
  args: {
    menus: basicMenus,
  },
  render: function WithFooterComponent() {
    const [selectedMenu, setSelectedMenu] = useState("home")

    return (
      <div style={{ background: "#f8f9fa", height: "500px", border: "1px solid #e9ecef", borderRadius: "8px" }}>
        <Sidebar
          title="Apollo"
          menus={basicMenus}
          selectedMenuKey={selectedMenu}
          onSelectMenu={(key) => setSelectedMenu(String(key))}
          footer={
            <MenuItemGroup icon={<User />} label="John Doe" selected>
              <MenuItem label="Profile" subItem />
              <MenuItem label="Settings" subItem />
            </MenuItemGroup>
          }
          onLogOut={() => alert("Logged out!")}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Sidebar with footer section containing user profile and logout functionality.",
      },
    },
  },
}

// Custom width
export const CustomWidth: Story = {
  args: {
    menus: basicMenus,
  },
  render: function CustomWidthComponent() {
    const [selectedMenu, setSelectedMenu] = useState("home")

    return (
      <div style={{ background: "#f8f9fa", height: "500px", border: "1px solid #e9ecef", borderRadius: "8px" }}>
        <Sidebar
          title="Wide Sidebar"
          width="400px"
          menus={basicMenus}
          selectedMenuKey={selectedMenu}
          onSelectMenu={(key) => setSelectedMenu(String(key))}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Sidebar with custom width for different layout requirements.",
      },
    },
  },
}

// Full featured
export const FullFeatured: Story = {
  args: {
    menus: complexMenus,
  },
  render: function FullFeaturedComponent() {
    const [selectedMenu, setSelectedMenu] = useState("products")
    const [expandedMenuKeys, setExpandedMenuKeys] = useState<string[]>(["ecommerce"])
    const [collapsed, setCollapsed] = useState(false)

    return (
      <div style={{ display: "flex", height: "500px", border: "1px solid #e9ecef", borderRadius: "8px" }}>
          <Sidebar
            title="Admin Dashboard"
            collapsible
            collapsed={collapsed}
            onCollapsedChange={setCollapsed}
            menus={complexMenus}
            selectedMenuKey={selectedMenu}
            onSelectMenu={(key) => setSelectedMenu(String(key))}
            expandedMenuKeys={expandedMenuKeys}
            onExpandedChange={(key, expanded) => {
              if (expanded) {
                setExpandedMenuKeys([...expandedMenuKeys, String(key)])
              } else {
                setExpandedMenuKeys(expandedMenuKeys.filter(k => k !== String(key)))
              }
            }}
            footer={
              <MenuItemGroup icon={<User />} label="Admin User" selected>
                <MenuItem label="My Profile" subItem />
                <MenuItem label="Account Settings" subItem />
              </MenuItemGroup>
            }
            onLogOut={() => alert("Admin logged out!")}
            logOutButtonLabel="Sign Out"
          />
        <div style={{ flex: 1, padding: "24px", backgroundColor: "#f8f9fa" }}>
          <Typography level="h3" style={{ marginBottom: "16px" }}>
            Main Content Area
          </Typography>
          <Typography level="body-1" style={{ color: "#6c757d" }}>
            Selected menu: <strong>{selectedMenu}</strong>
          </Typography>
          <Typography level="body-1" style={{ color: "#6c757d", marginTop: "8px" }}>
            Sidebar is {collapsed ? "collapsed" : "expanded"}
          </Typography>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Full-featured sidebar with all options including collapsible state, nested menus, footer, and main content integration.",
      },
    },
  },
}
