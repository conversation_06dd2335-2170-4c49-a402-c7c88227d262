import React from "react"
import { ComponentRules, UsageGuidelines } from "@/components"
import { ProductPrice } from "@apollo/storefront"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * ProductPrice component
 *
 * The ProductPrice component displays pricing information for products, including support for
 * units, discount pricing, and different currencies. It handles the visual
 * representation of both regular and discounted prices in e-commerce interfaces.
 *
 * Notes:
 * - Default currency is Thai Baht (฿);
 * - Supports optional unit display (e.g., kg, pack, piece);
 * - Shows discount pricing with crossed-out original price;
 * - Handles various currency symbols and formatting;
 * - Responsive design with proper text scaling.
 */
const meta = {
  title: "@apollo∕storefront/Components/Data Display/ProductPrice",
  component: ProductPrice,
  tags: ["autodocs"],
  globals: {
    brand: "storefront",
  },
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=2637-6138&m=dev",
    },
    docs: {
      description: {
        component:
          "The ProductPrice component renders pricing information with Apollo storefront styling. Supports price display, units, discount pricing, and multiple currencies for e-commerce interfaces.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { ProductPrice } from "@apollo/storefront"`}
            language="tsx"
          />
          <h2 id="productprice-props">Props</h2>
          <ArgTypes />
          <h2 id="productprice-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Always include the currency symbol to provide clear pricing context",
              "Use consistent number formatting (e.g., commas for thousands) across your application",
              "Display units when selling products by weight, volume, or quantity to clarify pricing",
              "Show discount pricing to highlight savings and encourage purchases",
              "Ensure price values are properly formatted strings, not raw numbers",
              "Use appropriate currency symbols for your target market and locale",
              "Keep pricing information prominent and easily scannable",
            ]}
          />
          <h2 id="productprice-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Ensure sufficient color contrast between price text and
                background for readability across all user conditions.
              </>,
              <>
                Use semantic markup that clearly indicates pricing information
                to screen readers and assistive technologies.
              </>,
              <>
                When displaying discount prices, ensure the crossed-out original
                price is still readable and provides context for the savings.
              </>,
              <>
                Consider providing additional context for screen readers when
                showing complex pricing (e.g., "Original price 249 baht, now 199
                baht").
              </>,
              <>
                Maintain consistent text sizing that scales appropriately with
                user font size preferences and zoom levels.
              </>,
              <>
                Ensure currency symbols and units are announced correctly by
                screen readers in the user's language and locale.
              </>,
            ]}
          />
          <h2 id="productprice-examples">Examples</h2>
          <Stories title="" />
          <h2 id="productprice-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <ProductPrice price="299" currency="฿" />
                    </div>
                  ),
                  description:
                    "Use clear, properly formatted price values with currency symbols",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <ProductPrice price="299.00000" currency="" />
                    </div>
                  ),
                  description:
                    "Avoid excessive decimal places and missing currency symbols",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <ProductPrice price="85" unit="kg" currency="฿" />
                    </div>
                  ),
                  description:
                    "Include units when selling by weight, volume, or quantity",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <ProductPrice price="85" currency="฿" />
                    </div>
                  ),
                  description:
                    "Don't omit units when the pricing context requires clarification",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <ProductPrice
                        price="199"
                        discountPrice="249"
                        currency="฿"
                      />
                    </div>
                  ),
                  description:
                    "Show discount pricing to highlight savings clearly",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <ProductPrice
                        price="199"
                        discountPrice="200"
                        currency="฿"
                      />
                    </div>
                  ),
                  description:
                    "Avoid showing minimal discounts that don't provide meaningful savings",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    price: {
      control: { type: "text" },
      description: "The main price value to display.",
      table: {
        type: { summary: "string" },
      },
    },
    unit: {
      control: { type: "text" },
      description: "The unit of measurement (e.g., 'kg', 'pack', 'piece').",
      table: {
        type: { summary: "string" },
      },
    },
    discountPrice: {
      control: { type: "text" },
      description: "The original price before discount (shown crossed out).",
      table: {
        type: { summary: "string" },
      },
    },
    currency: {
      control: { type: "text" },
      description: "The currency symbol. Default is Thai Baht (฿).",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "฿" },
      },
    },
  },
  args: {
    price: "299",
    currency: "฿",
  },
} satisfies Meta<typeof ProductPrice>

export default meta

type Story = StoryObj<typeof ProductPrice>

/** Default ProductPrice (demonstrates essential pricing information) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview ProductPrice with default settings. Shows essential pricing information with currency symbol. This is the most common use case for displaying product prices.",
      },
    },
  },
  args: {
    price: "299",
    currency: "฿",
  },
}

/** ProductPrice with unit of measurement for weight, volume, or quantity-based pricing */
export const WithUnit: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "ProductPrice with unit of measurement displayed. Useful for products sold by weight, volume, or specific quantities.",
      },
    },
  },
  args: {
    price: "85",
    unit: "kg",
    currency: "฿",
  },
}

/** ProductPrice showing discounted pricing with original price crossed out */
export const WithDiscount: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "ProductPrice displaying discounted pricing with both current price and original price crossed out to highlight savings.",
      },
    },
  },
  args: {
    price: "199",
    discountPrice: "249",
    currency: "฿",
  },
}

/** ProductPrice with both unit and discount pricing for comprehensive product information */
export const WithUnitAndDiscount: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "ProductPrice combining unit pricing with discount information, showing the complete pricing context for unit-based products on sale.",
      },
    },
  },
  args: {
    price: "159",
    unit: "pack",
    discountPrice: "199",
    currency: "฿",
  },
}

/** ProductPrice with different currency symbols for international markets */
export const DifferentCurrencies: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates ProductPrice with various currency symbols for international e-commerce applications and multi-market support.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "24px",
        flexWrap: "wrap",
        alignItems: "center",
      }}
    >
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Thai Baht (฿)</h4>
        <ProductPrice price="299" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>US Dollar ($)</h4>
        <ProductPrice price="8.50" currency="$" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Euro (€)</h4>
        <ProductPrice price="7.90" currency="€" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          British Pound (£)
        </h4>
        <ProductPrice price="6.75" currency="£" />
      </div>
    </div>
  ),
}

/** Various pricing scenarios commonly used in e-commerce applications */
export const PricingScenarios: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases different pricing scenarios including simple pricing, unit-based pricing, bulk pricing, and weight/volume-based pricing commonly used in e-commerce.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "24px",
        flexWrap: "wrap",
        alignItems: "flex-start",
      }}
    >
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Simple Price</h4>
        <ProductPrice price="50" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Price per Unit
        </h4>
        <ProductPrice price="25" unit="piece" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Bulk Pricing</h4>
        <ProductPrice price="450" unit="dozen" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Weight-based</h4>
        <ProductPrice price="120" unit="kg" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Volume-based</h4>
        <ProductPrice price="35" unit="liter" currency="฿" />
      </div>
    </div>
  ),
}

/** Discount pricing examples showing different discount amounts and savings */
export const DiscountExamples: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates different discount pricing scenarios including small, medium, and large discounts, plus unit-based discount pricing.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "24px",
        flexWrap: "wrap",
        alignItems: "flex-start",
      }}
    >
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Small Discount
        </h4>
        <ProductPrice price="95" discountPrice="99" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Medium Discount
        </h4>
        <ProductPrice price="199" discountPrice="249" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Large Discount
        </h4>
        <ProductPrice price="299" discountPrice="499" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>With Unit</h4>
        <ProductPrice price="89" unit="pack" discountPrice="119" currency="฿" />
      </div>
    </div>
  ),
}

/** High-value product pricing to test layout with larger numbers and formatting */
export const HighValuePricing: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates ProductPrice with high-value pricing to test layout and formatting with larger numbers, including thousands and complex pricing scenarios.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "24px",
        flexWrap: "wrap",
        alignItems: "flex-start",
      }}
    >
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Thousands</h4>
        <ProductPrice price="2,499" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>With Discount</h4>
        <ProductPrice price="8,999" discountPrice="12,999" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Per Unit</h4>
        <ProductPrice price="1,250" unit="set" currency="฿" />
      </div>
    </div>
  ),
}

/** Comprehensive showcase of ProductPrice configurations and features */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of ProductPrice configurations including different pricing types, currencies, units, and discount scenarios.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 20,
          alignItems: "flex-start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <h4 style={{ margin: 0, fontSize: "14px", fontWeight: "600" }}>
            Basic Pricing
          </h4>
          <ProductPrice price="299" currency="฿" />
          <ProductPrice price="8.50" currency="$" />
          <ProductPrice price="7.90" currency="€" />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <h4 style={{ margin: 0, fontSize: "14px", fontWeight: "600" }}>
            Unit Pricing
          </h4>
          <ProductPrice price="85" unit="kg" currency="฿" />
          <ProductPrice price="25" unit="piece" currency="฿" />
          <ProductPrice price="450" unit="dozen" currency="฿" />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <h4 style={{ margin: 0, fontSize: "14px", fontWeight: "600" }}>
            Discount Pricing
          </h4>
          <ProductPrice price="199" discountPrice="249" currency="฿" />
          <ProductPrice
            price="89"
            unit="pack"
            discountPrice="119"
            currency="฿"
          />
          <ProductPrice price="8,999" discountPrice="12,999" currency="฿" />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <h4 style={{ margin: 0, fontSize: "14px", fontWeight: "600" }}>
            High Value
          </h4>
          <ProductPrice price="2,499" currency="฿" />
          <ProductPrice price="45,999" currency="฿" />
          <ProductPrice price="1,250" unit="set" currency="฿" />
        </div>
      </div>
    )
  },
}
