import React from "react"
import { ComponentRules, UsageGuidelines } from "@/components"
import { ProductCardContent } from "@apollo/storefront"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * ProductCardContent displays the textual content of a product card including
 * title, caption, and pricing information. It handles text truncation and layout
 * for product information display.
 */
const meta: Meta<typeof ProductCardContent> = {
  title: "@apollo∕storefront/Components/Data Display/ProductCardContent",
  component: ProductCardContent,
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=2637-6138&m=dev",
    },
    docs: {
      description: {
        component:
          "The ProductCardContent component renders textual product information with Apollo storefront styling. Supports title, caption, and pricing with configurable text truncation for consistent layouts.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { ProductCardContent } from "@apollo/storefront"`}
            language="tsx"
          />
          <h2 id="productcardcontent-props">Props</h2>
          <ArgTypes />
          <h2 id="productcardcontent-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use clear, descriptive product titles that help users understand the product",
              "Keep captions concise and focused on key product features or benefits",
              "Configure appropriate line limits based on your layout requirements",
              "Ensure pricing information is accurate and properly formatted",
              "Use consistent typography hierarchy across product listings",
              "Consider text length when setting titleMaxLines and captionMaxLines",
            ]}
          />
          <h2 id="productcardcontent-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Ensure product titles are descriptive and provide sufficient
                context about the product without relying on visual elements.
              </>,
              <>
                Use meaningful captions that add value and context rather than
                repeating the title information.
              </>,
              <>
                Maintain proper text contrast ratios for all text elements
                including titles, captions, and pricing.
              </>,
              <>
                Consider screen reader users when truncating text - ensure
                essential information is not cut off.
              </>,
              <>
                Ensure pricing information is clearly associated with the
                corresponding product through proper markup.
              </>,
            ]}
          />
          <h2 id="productcardcontent-examples">Examples</h2>
          <Stories title="" />
          <h2 id="productcardcontent-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div
                      style={{
                        width: 200,
                        border: "1px solid #e0e0e0",
                        padding: 16,
                      }}
                    >
                      <ProductCardContent
                        title="Premium Organic Coffee Beans"
                        caption="Single origin arabica from Ethiopian highlands"
                        price={{ price: "299", currency: "฿", unit: "pack" }}
                      />
                    </div>
                  ),
                  description: "Use descriptive titles and meaningful captions",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        width: 200,
                        border: "1px solid #e0e0e0",
                        padding: 16,
                      }}
                    >
                      <ProductCardContent
                        title="Product"
                        caption="Item"
                        price={{ price: "299", currency: "฿" }}
                      />
                    </div>
                  ),
                  description:
                    "Avoid generic titles and captions that don't provide useful information",
                },
              },
              {
                positive: {
                  component: (
                    <div
                      style={{
                        width: 200,
                        border: "1px solid #e0e0e0",
                        padding: 16,
                      }}
                    >
                      <ProductCardContent
                        title="Artisan Coffee Blend - Premium Quality Beans from Multiple Origins"
                        titleMaxLines={2}
                        price={{ price: "350", currency: "฿" }}
                      />
                    </div>
                  ),
                  description:
                    "Configure appropriate line limits for consistent layouts",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        width: 200,
                        border: "1px solid #e0e0e0",
                        padding: 16,
                      }}
                    >
                      <ProductCardContent
                        title="Artisan Coffee Blend - Premium Quality Beans from Multiple Origins with Extended Description"
                        titleMaxLines={5}
                        price={{ price: "350", currency: "฿" }}
                      />
                    </div>
                  ),
                  description:
                    "Avoid excessive line limits that can break layout consistency",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  globals: {
    brand: "storefront",
  },
  decorators: [
    (Story) => (
      <div
        style={{
          width: 300,
          padding: 16,
          border: "1px solid #e0e0e0",
          position: "relative",
        }}
      >
        <span
          style={{
            position: "absolute",
            top: "-8px",
            backgroundColor: "black",
            borderRadius: "4px",
            color: "white",
            padding: "2px 6px",
            fontFamily: "Arial, sans-serif",
            fontSize: "8px",
          }}
        >
          this line is added from storybook
        </span>
        <Story />
      </div>
    ),
  ],
  tags: ["autodocs"],
  argTypes: {
    title: {
      control: "text",
      description: "The product title",
    },
    caption: {
      control: "text",
      description: "Additional caption or description for the product",
    },
    titleMaxLines: {
      control: "number",
      description: "Maximum number of lines for the title",
    },
    captionMaxLines: {
      control: "number",
      description: "Maximum number of lines for the caption",
    },
    price: {
      control: "object",
      description: "Price information object",
    },
  },
} satisfies Meta<typeof ProductCardContent>

export default meta
type Story = StoryObj<typeof ProductCardContent>

export const Default: Story = {
  args: {
    title: "Premium Organic Coffee Beans",
    caption:
      "Single origin arabica coffee beans from the highlands of Ethiopia",
    price: {
      price: "299",
      currency: "฿",
      unit: "pack",
    },
  },
}

/**
 * ProductCardContent with a long title that will be truncated based on titleMaxLines.
 */
export const LongTitle: Story = {
  args: {
    title:
      "Premium Organic Single Origin Fair Trade Ethiopian Highland Arabica Coffee Beans with Natural Processing Method",
    caption: "High quality coffee beans",
    titleMaxLines: 2,
    price: {
      price: "450",
      currency: "฿",
    },
  },
}

/**
 * ProductCardContent with both title and long caption that will be truncated.
 */
export const LongCaption: Story = {
  args: {
    title: "Artisan Coffee Blend",
    caption:
      "A carefully crafted blend of premium coffee beans from multiple origins, featuring notes of chocolate, caramel, and citrus. Perfect for both espresso and filter brewing methods. Roasted to perfection daily in small batches.",
    captionMaxLines: 2,
    price: {
      price: "350",
      currency: "฿",
      unit: "bag",
    },
  },
}

/**
 * ProductCardContent with discount pricing to show both regular and discounted prices.
 */
export const WithDiscount: Story = {
  args: {
    title: "House Blend Coffee",
    caption: "Our signature house blend",
    price: {
      price: "199",
      discountPrice: "249",
      currency: "฿",
      unit: "pack",
    },
  },
}

/**
 * ProductCardContent with only title and price, no caption.
 */
export const TitleOnly: Story = {
  args: {
    title: "Espresso Blend",
    price: {
      price: "180",
      currency: "฿",
    },
  },
}

/**
 * ProductCardContent with no price information.
 */
export const NoPrice: Story = {
  args: {
    title: "Coffee Subscription",
    caption: "Monthly delivery of fresh coffee beans",
  },
}

/**
 * Different line limit configurations shown side by side.
 */
export const LineLimits: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", flexWrap: "wrap" }}>
      <div style={{ width: 200, border: "1px solid #e0e0e0", padding: 16 }}>
        <h4 style={{ margin: "0 0 16px 0", fontSize: "14px" }}>
          Default (3 lines title)
        </h4>
        <ProductCardContent
          title="Very Long Product Title That Should Be Truncated After Three Lines"
          caption="Short caption"
          titleMaxLines={3}
        />
      </div>
      <div style={{ width: 200, border: "1px solid #e0e0e0", padding: 16 }}>
        <h4 style={{ margin: "0 0 16px 0", fontSize: "14px" }}>
          Limited (1 line title)
        </h4>
        <ProductCardContent
          title="Very Long Product Title That Should Be Truncated After One Line"
          caption="Short caption"
          titleMaxLines={1}
        />
      </div>
      <div style={{ width: 200, border: "1px solid #e0e0e0", padding: 16 }}>
        <h4 style={{ margin: "0 0 16px 0", fontSize: "14px" }}>
          Extended (2 lines caption)
        </h4>
        <ProductCardContent
          title="Product Title"
          caption="Very long caption that should be truncated after exactly two lines of text content"
          captionMaxLines={2}
        />
      </div>
    </div>
  ),
}
