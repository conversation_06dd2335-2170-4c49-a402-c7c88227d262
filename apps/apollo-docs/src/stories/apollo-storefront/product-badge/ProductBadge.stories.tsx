import React from "react"
import { ComponentRules, UsageGuidelines } from "@/components"
import { ProductBadge } from "@apollo/storefront"
import { Typography } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * ProductBadge component
 *
 * The ProductBadge component displays badges on products to highlight special states like discounts,
 * low stock, monthly offers, or indicate when there are more items available.
 * Built on top of the Apollo UI Badge component with product-specific styling variants optimized for mobile usage.
 *
 */
const meta = {
  title: "@apollo∕storefront/Components/Data Display/ProductBadge",
  component: ProductBadge,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=2582-16117&m=dev",
    },
    docs: {
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { ProductBadge } from "@apollo/storefront"`} language="tsx" />
          <h2 id="product-badge-props">Props</h2>
          <ArgTypes />
          <h2 id="product-badge-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use clear, concise labels that communicate product status effectively",
              "Choose appropriate variants that match the product context (discount for sales, lowStock for inventory alerts)",
              "Keep badge text short and scannable on mobile devices",
              "Use consistent variant meanings across your e-commerce application",
              "Consider touch target size when using badges as interactive elements on mobile",
              "Ensure sufficient color contrast for accessibility on all device types",
            ]}
          />
          <h2 id="product-badge-examples">Examples</h2>
          <Stories title="" />
          <h2 id="product-badge-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center", flexWrap: "wrap" }}>
                      <ProductBadge variant="discount" label="-20%" />
                      <ProductBadge variant="lowStock" label="Only 2 left" />
                      <ProductBadge variant="monthly" label="Monthly Deal" />
                    </div>
                  ),
                  description: "Use variants that match the semantic meaning and product context",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center", flexWrap: "wrap" }}>
                      <ProductBadge variant="lowStock" label="-20%" />
                      <ProductBadge variant="discount" label="Only 2 left" />
                      <ProductBadge variant="moreItem" label="Monthly Deal" />
                    </div>
                  ),
                  description: "Don't use variants that conflict with the product information being displayed",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center", flexWrap: "wrap" }}>
                      <ProductBadge variant="discount" label="-15%" />
                      <ProductBadge variant="moreItem" label="+3" />
                      <ProductBadge variant="lowStock" label="Last one" />
                    </div>
                  ),
                  description: "Keep labels concise and scannable, especially for mobile users",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center", flexWrap: "wrap" }}>
                      <ProductBadge variant="discount" label="15% discount available" />
                      <ProductBadge variant="moreItem" label="+3 more items available" />
                      <ProductBadge variant="lowStock" label="Only one item remaining in stock" />
                    </div>
                  ),
                  description: "Don't use overly long labels that may be difficult to read on mobile devices",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  globals: {
    brand: "storefront",
  },
  argTypes: {
    label: {
      control: { type: "text" },
      description: "The text content to display inside the badge.",
      table: {
        type: { summary: "string" },
      },
    },
    variant: {
      control: { type: "radio" },
      options: ["moreItem", "discount", "monthly", "lowStock"],
      description: "Product-specific variant style of the badge. Default is 'moreItem'.",
      table: {
        type: { summary: '"moreItem" | "discount" | "monthly" | "lowStock"' },
        defaultValue: { summary: "moreItem" },
      },
    },
    color: {
      control: { type: "radio" },
      options: ["default", "process", "success", "warning", "error"],
      description: "Inherited from Badge component - color theme of the badge.",
      table: {
        type: { summary: '"default" | "process" | "success" | "warning" | "error"' },
        defaultValue: { summary: "default" },
      },
    },
    icon: {
      control: false,
      description: "Inherited from Badge component - optional icon element to display alongside the label.",
      table: { type: { summary: "ReactNode" } },
    },
    children: {
      control: false,
      description: "Inherited from Badge component - when provided, the badge will be positioned as an overlay on the children.",
      table: { type: { summary: "ReactNode" } },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS classes to apply to the badge.",
      table: {
        type: { summary: "string" },
      },
    },
  },
} satisfies Meta<typeof ProductBadge>

export default meta

type Story = StoryObj<typeof ProductBadge>

/** Default ProductBadge (demonstrates moreItem variant) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview ProductBadge with default settings. The component defaults to variant 'moreItem' and displays as a standalone badge optimized for mobile usage.",
      },
    },
  },
  args: {
    variant: "moreItem",
    label: "+3 more",
  },
}

/** ProductBadge with different variants (moreItem, discount, monthly, lowStock) */
export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases all available product-specific variants: moreItem, discount, monthly, and lowStock. Each variant conveys different product context and uses appropriate color schemes for e-commerce applications.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center", flexWrap: "wrap" }}>
      <ProductBadge {...args} label="+3 more" variant="moreItem" />
      <ProductBadge {...args} label="-20%" variant="discount" />
      <ProductBadge {...args} label="Monthly Deal" variant="monthly" />
      <ProductBadge {...args} label="Only 2 left" variant="lowStock" />
    </div>
  ),
}

/** ProductBadge in real-world e-commerce contexts */
export const UseCases: Story = {
  parameters: {
    docs: {
      description: {
        story: "Real-world usage examples showing ProductBadge in different e-commerce scenarios with appropriate labels and contexts.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: 24,
        alignItems: "start",
      }}
    >
      <div>
        <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Inventory Status</Typography>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <ProductBadge {...args} label="Last one" variant="lowStock" />
          <ProductBadge {...args} label="Only 3 left" variant="lowStock" />
          <ProductBadge {...args} label="Low stock" variant="lowStock" />
        </div>
      </div>
      <div>
        <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Promotions & Discounts</Typography>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <ProductBadge {...args} label="-15%" variant="discount" />
          <ProductBadge {...args} label="-50%" variant="discount" />
          <ProductBadge {...args} label="Sale" variant="discount" />
          <ProductBadge {...args} label="Monthly Deal" variant="monthly" />
          <ProductBadge {...args} label="Special Offer" variant="monthly" />
        </div>
      </div>
      <div>
        <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Product Variants</Typography>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <ProductBadge {...args} label="+2 colors" variant="moreItem" />
          <ProductBadge {...args} label="+5 sizes" variant="moreItem" />
          <ProductBadge {...args} label="+3 more" variant="moreItem" />
        </div>
      </div>
    </div>
  ),
}

/** Mobile-optimized ProductBadge examples */
export const MobileOptimized: Story = {
  globals: {
    viewport: { value: "mobile2", isRotated: false },
  },
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "ProductBadge examples optimized for mobile usage, demonstrating appropriate sizing, touch targets, and responsive behavior.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: 16,
        alignItems: "start",
        padding: "16px",
        border: "1px dashed #ccc",
        borderRadius: "8px",
      }}
    >
      <div style={{ width: "100%" }}>
        <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Mobile Product Grid</Typography>
        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 12 }}>
          {[
            { variant: "discount", label: "-25%" },
            { variant: "lowStock", label: "Last one" },
            { variant: "monthly", label: "Deal" },
            { variant: "moreItem", label: "+2" },
          ].map((badge, index) => (
            <div
              key={`${badge.variant}-${badge.label}`}
              style={{
                position: "relative",
                width: "100%",
                height: "120px",
                backgroundColor: "#f5f5f5",
                borderRadius: "8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <ProductBadge
                {...args}
                variant={badge.variant as any}
                label={badge.label}
                style={{
                  position: "absolute",
                  top: "8px",
                  right: "8px",
                }}
              />
              <Typography level="bodySmall" style={{ color: "#666" }}>Product {index + 1}</Typography>
            </div>
          ))}
        </div>
      </div>
    </div>
  ),
}