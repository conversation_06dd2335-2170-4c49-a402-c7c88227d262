import React from "react"
import { ComponentRules, UsageGuidelines } from "@/components"
import {
  ProductBadge,
  ProductCard,
} from "@apollo/storefront"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * ProductCard component
 *
 * The ProductCard component displays product information in a card format, typically used in
 * product listings, search results, and e-commerce interfaces. It supports
 * product images, titles, captions, pricing, and customizable layouts.
 *
 * Notes:
 * - Default fill is false (fixed width);
 * - Default titleMaxLines is 3;
 * - Default captionMaxLines is 2;
 * - Supports custom render functions for image, content, and footer;
 * - Includes support for image overlays and badges;
 * - Responsive design with mobile-friendly layouts.
 */
const meta = {
  title: "@apollo∕storefront/Components/Data Display/ProductCard",
  component: ProductCard,
  tags: ["autodocs"],
  globals: {
    brand: "storefront",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-<PERSON>as-Storefront?node-id=2637-6138&m=dev",
    },
    docs: {
      description: {
        component:
          "The ProductCard component renders a product card with Apollo storefront styling. Supports product images, titles, captions, pricing, and customizable layouts for e-commerce interfaces.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { ProductCard } from "@apollo/storefront"`} language="tsx" />
          <h2 id="productcard-props">Props</h2>
          <ArgTypes />
          <h2 id="productcard-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use clear, descriptive product titles that help users understand what they're viewing",
              "Include high-quality product images with appropriate alt text for accessibility",
              "Use captions to provide additional context or key product features",
              "Display pricing information clearly with proper currency formatting",
              "Use the fill prop for responsive grid layouts",
              "Implement proper image overlays for promotional badges or status indicators",
              "Keep product information concise and scannable",
            ]}
          />
          <h2 id="productcard-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide descriptive <code>imageAlt</code> text that clearly describes
                the product image for screen readers.
              </>,
              <>
                Ensure product titles are descriptive and provide sufficient context
                about the product without relying solely on the image.
              </>,
              <>
                Use proper semantic markup and ensure interactive elements like buttons
                in the footer are keyboard accessible.
              </>,
              <>
                Maintain sufficient color contrast for all text elements including
                titles, captions, and pricing information.
              </>,
              <>
                When using custom render functions, ensure they maintain accessibility
                standards and proper focus management.
              </>,
              <>
                Provide meaningful labels for any interactive elements within
                image overlays or custom content areas.
              </>,
            ]}
          />
          <h2 id="productcard-examples">Examples</h2>
          <Stories title="" />
          <h2 id="productcard-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <ProductCard
                        title="Premium Bluetooth Headphones"
                        caption="High-quality audio experience"
                        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop"
                        imageAlt="Premium bluetooth headphones"
                        price={{ price: "1,599", currency: "฿" }}
                      />
                    </div>
                  ),
                  description: "Use descriptive titles and meaningful captions",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <ProductCard
                        title="Product"
                        caption="Item"
                        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop"
                        imageAlt="Product image"
                        price={{ price: "1,599", currency: "฿" }}
                      />
                    </div>
                  ),
                  description:
                    "Avoid generic titles and captions that don't provide useful information",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <ProductCard
                        title="Organic Coffee Beans"
                        imageSrc="https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=300&fit=crop"
                        imageAlt="Organic coffee beans in a burlap sack"
                        price={{ price: "299", currency: "฿" }}
                      />
                    </div>
                  ),
                  description: "Provide descriptive alt text for images",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <ProductCard
                        title="Organic Coffee Beans"
                        imageSrc="https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=300&fit=crop"
                        imageAlt="Image"
                        price={{ price: "299", currency: "฿" }}
                      />
                    </div>
                  ),
                  description:
                    "Avoid generic or non-descriptive alt text",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    imageSrc: {
      control: { type: "text" },
      description: "Source URL for the product image.",
      table: {
        type: { summary: "string" },
      },
    },
    imageAlt: {
      control: { type: "text" },
      description: "Alternative text for the product image for accessibility.",
      table: {
        type: { summary: "string" },
      },
    },
    title: {
      control: { type: "text" },
      description: "The product title displayed on the card.",
      table: {
        type: { summary: "string" },
      },
    },
    caption: {
      control: { type: "text" },
      description: "Additional caption or description for the product.",
      table: {
        type: { summary: "string" },
      },
    },
    fill: {
      control: { type: "boolean" },
      description: "Whether the card should fill the available width. Default is false.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    titleMaxLines: {
      control: { type: "number" },
      description: "Maximum number of lines for the title. Default is 3.",
      table: {
        type: { summary: "number" },
        defaultValue: { summary: "3" },
      },
    },
    captionMaxLines: {
      control: { type: "number" },
      description: "Maximum number of lines for the caption. Default is 2.",
      table: {
        type: { summary: "number" },
        defaultValue: { summary: "2" },
      },
    },
    price: {
      control: { type: "object" },
      description: "Price information object containing price, currency, unit, and discount details.",
      table: {
        type: { summary: "ProductPriceProps" },
      },
    },
    renderImageOverlay: {
      control: false,
      description: "Function to render custom overlay content on the image.",
      table: {
        type: { summary: "() => ReactElement" },
      },
    },
    renderImage: {
      control: false,
      description: "Function to render custom image component.",
      table: {
        type: { summary: "(props: ProductImageProps) => ReactElement" },
      },
    },
    renderContent: {
      control: false,
      description: "Function to render custom content component.",
      table: {
        type: { summary: "(props: ProductCardContentProps) => ReactElement" },
      },
    },
    renderFooter: {
      control: false,
      description: "Function to render custom footer content.",
      table: {
        type: { summary: "() => ReactElement" },
      },
    },
  },
  args: {
    title: "Premium Product",
    fill: false,
    titleMaxLines: 3,
    captionMaxLines: 2,
  },
} satisfies Meta<typeof ProductCard>

export default meta

type Story = StoryObj<typeof ProductCard>

/** Default ProductCard (demonstrates essential product information) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview ProductCard with default settings. Shows essential product information including image, title, and price. This is the most common use case for an e-commerce product card.",
      },
    },
  },
  args: {
    title: "Premium Bluetooth Headphones",
    imageSrc:
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop",
    imageAlt: "Premium bluetooth headphones product image",
    price: {
      price: "1,599",
      currency: "฿",
    },
  },
}

/** ProductCard with complete product information including caption and pricing */
export const WithCaption: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "ProductCard with complete product information including title, caption, and pricing details.",
      },
    },
  },
  args: {
    title: "Thai Jasmine Rice Premium Quality",
    caption: "Premium organic rice from northern Thailand",
    imageSrc:
      "https://images.unsplash.com/photo-1586201375761-83865001e31c?w=300&h=300&fit=crop",
    imageAlt: "Thai jasmine rice product image",
    price: {
      price: "120",
      unit: "กล่อง",
      currency: "฿",
    },
  },
}

/** ProductCard showing discounted pricing with original and sale prices */
export const WithDiscount: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "ProductCard displaying discounted pricing with both current price and original price crossed out.",
      },
    },
  },
  args: {
    title: "Organic Coffee Beans",
    caption: "Limited time offer - Premium arabica beans",
    imageSrc:
      "https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=300&fit=crop",
    imageAlt: "Organic coffee beans product image",
    price: {
      price: "95",
      discountPrice: "120",
      unit: "ถุง",
      currency: "฿",
    },
  },
}

/**
 * ProductCard with a very long title to demonstrate text truncation.
 */
export const LongTitle: Story = {
  args: {
    title:
      "Ultra Premium Wireless Noise Cancelling Bluetooth Headphones with Advanced Active Noise Control Technology",
    caption: "Professional grade audio equipment",
    imageSrc:
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop",
    imageAlt: "Premium headphones product image",
    price: {
      price: "2,499",
      currency: "฿",
    },
    titleMaxLines: 2,
  },
}

/** ProductCard with fill width behavior for responsive layouts */
export const FillWidth: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "ProductCard that fills the available width, useful for responsive grid layouts and flexible containers.",
      },
    },
  },
  args: {
    title: "Smart Watch Series 8",
    caption: "Latest generation with health monitoring",
    imageSrc:
      "https://images.unsplash.com/photo-**********-31a4b719223d?w=300&h=300&fit=crop",
    imageAlt: "Smart watch product image",
    price: {
      price: "12,900",
      currency: "฿",
    },
    fill: true,
  },
  render: (args) => (
    <div style={{ width: "400px", padding: "16px", background: "#f5f5f5" }}>
      <ProductCard {...args} />
    </div>
  ),
}

/** ProductCard without image showing text-only layout */
export const NoImage: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "ProductCard without an image, demonstrating text-only layout for digital products or services.",
      },
    },
  },
  args: {
    title: "Digital Download Software",
    caption: "Instant download after purchase",
    price: {
      price: "599",
      currency: "฿",
    },
  },
}

/** ProductCard with different layouts and configurations */
export const Layouts: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases different ProductCard layouts including with and without captions, various image configurations, and pricing displays.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "flex-start", flexWrap: "wrap" }}>
      <ProductCard
        {...args}
        title="Minimal Card"
        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop"
        imageAlt="Minimal product"
        price={{ price: "299", currency: "฿" }}
      />
      <ProductCard
        {...args}
        title="With Caption"
        caption="Additional product details"
        imageSrc="https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=300&fit=crop"
        imageAlt="Product with caption"
        price={{ price: "450", currency: "฿" }}
      />
      <ProductCard
        {...args}
        title="Text Only Product"
        caption="No image required"
        price={{ price: "199", currency: "฿" }}
      />
    </div>
  ),
}

/** ProductCard with pricing variations */
export const PricingVariations: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates different pricing configurations including simple pricing, unit pricing, and discount pricing.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "flex-start", flexWrap: "wrap" }}>
      <ProductCard
        {...args}
        title="Simple Price"
        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop"
        imageAlt="Simple pricing product"
        price={{ price: "299", currency: "฿" }}
      />
      <ProductCard
        {...args}
        title="Unit Pricing"
        imageSrc="https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=300&fit=crop"
        imageAlt="Unit pricing product"
        price={{ price: "85", unit: "kg", currency: "฿" }}
      />
      <ProductCard
        {...args}
        title="Discount Price"
        imageSrc="https://images.unsplash.com/photo-1586201375761-83865001e31c?w=300&h=300&fit=crop"
        imageAlt="Discounted product"
        price={{ price: "199", discountPrice: "249", currency: "฿" }}
      />
    </div>
  ),
}

/** Comprehensive showcase of ProductCard configurations and features */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of ProductCard configurations including different content types, layouts, and feature combinations.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 20,
          alignItems: "flex-start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <h4 style={{ margin: 0, fontSize: "14px", fontWeight: "600" }}>Basic Cards</h4>
          <ProductCard
            title="Standard Product"
            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop"
            imageAlt="Standard product"
            price={{ price: "299", currency: "฿" }}
          />
          <ProductCard
            title="With Caption"
            caption="Additional details"
            imageSrc="https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=300&fit=crop"
            imageAlt="Product with caption"
            price={{ price: "450", currency: "฿" }}
          />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <h4 style={{ margin: 0, fontSize: "14px", fontWeight: "600" }}>Pricing Variants</h4>
          <ProductCard
            title="Unit Pricing"
            imageSrc="https://images.unsplash.com/photo-1586201375761-83865001e31c?w=300&h=300&fit=crop"
            imageAlt="Unit pricing product"
            price={{ price: "85", unit: "kg", currency: "฿" }}
          />
          <ProductCard
            title="Discount Price"
            imageSrc="https://images.unsplash.com/photo-**********-31a4b719223d?w=300&h=300&fit=crop"
            imageAlt="Discounted product"
            price={{ price: "199", discountPrice: "249", currency: "฿" }}
          />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <h4 style={{ margin: 0, fontSize: "14px", fontWeight: "600" }}>Special Cases</h4>
          <ProductCard
            title="No Image Product"
            caption="Digital or service product"
            price={{ price: "599", currency: "฿" }}
          />
          <ProductCard
            fill
            title="Fill Width Card"
            imageSrc="https://images.unsplash.com/photo-1527814050087-3793815479db?w=300&h=300&fit=crop"
            imageAlt="Fill width product"
            price={{ price: "890", currency: "฿" }}
          />
        </div>
      </div>
    )
  },
}

/**
 * ProductCard with custom line limits for title and caption.
 */
export const CustomLineLimit: Story = {
  args: {
    title: "Professional DSLR Camera Kit with Multiple Lenses and Accessories",
    caption:
      "Complete photography bundle including camera body, three premium lenses, tripod, camera bag, and professional lighting equipment",
    imageSrc:
      "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=300&h=300&fit=crop",
    imageAlt: "DSLR camera kit product image",
    price: {
      price: "45,999",
      currency: "฿",
    },
    titleMaxLines: 1,
    captionMaxLines: 3,
  },
}

/**
 * Multiple ProductCards in a grid layout to show typical usage.
 */
export const ProductGrid: Story = {
  render: () => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(177px, 1fr))",
        gap: "16px",
        padding: "16px",
        background: "#f8f9fa",
      }}
    >
      <ProductCard
        fill
        title="Wireless Mouse"
        imageSrc="https://images.unsplash.com/photo-1527814050087-3793815479db?w=300&h=300&fit=crop"
        imageAlt="Wireless mouse"
        price={{ price: "890", currency: "฿" }}
      />
      <ProductCard
        fill
        title="Mechanical Keyboard"
        caption="RGB backlit"
        imageSrc="https://images.unsplash.com/photo-1587829741301-dc798b83add3?w=300&h=300&fit=crop"
        imageAlt="Mechanical keyboard"
        price={{ price: "2,490", currency: "฿" }}
      />
      <ProductCard
        fill
        title="4K Monitor"
        caption="27-inch display"
        imageSrc="https://images.unsplash.com/photo-1527443224154-c4a3942d3acf?w=300&h=300&fit=crop"
        imageAlt="4K monitor"
        price={{ price: "8,990", discountPrice: "9,990", currency: "฿" }}
      />
      <ProductCard
        fill
        title="Ergonomic Chair"
        imageSrc="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=300&fit=crop"
        imageAlt="Ergonomic office chair"
        price={{ price: "12,500", currency: "฿" }}
      />
    </div>
  ),
}

/**
 * Mobile responsive layout showing different card behaviors.
 */
export const OnMobile: Story = {
  globals: {
    viewport: { value: "mobile2", isRotated: false },
  },
  args: {
    title: "Wireless Headphones",
    caption: "Premium sound quality",
    imageSrc:
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop",
    imageAlt: "Wireless headphones",
    price: {
      price: "1,299",
      currency: "฿",
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        padding: "8px",
        gap: "16px",
      }}
    >
      <h3 style={{ margin: 0, fontSize: "16px", fontWeight: "600" }}>
        Mobile Layout Examples
      </h3>

      {/* Fixed width cards */}
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "start",
          alignItems: "start",
          gap: "12px",
          overflowX: "auto",
          paddingBottom: "8px",
        }}
      >
        <ProductCard {...args} />
        <ProductCard
          {...args}
          title="Bluetooth Speaker"
          imageSrc="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=300&h=300&fit=crop"
        />
        <ProductCard
          {...args}
          title="Smart Watch"
          imageSrc="https://images.unsplash.com/photo-**********-31a4b719223d?w=300&h=300&fit=crop"
        />
      </div>

      {/* Full width card */}
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px", color: "#666" }}>
          Full Width Card:
        </h4>
        <ProductCard {...args} fill />
      </div>
    </div>
  ),
}

// --- RENDER FUNCTION STORIES ---

/**
 * ProductCard with custom image rendering including badges and overlays.
 */
export const CustomImageRender: Story = {
  render: () => (
    <ProductCard
      title="Limited Edition Sneakers"
      caption="Only 100 pairs available"
      price={{
        price: "4,999",
        currency: "฿",
      }}
      renderImage={({ imageSrc, imageAlt }) => (
        <div style={{ position: "relative", width: "100%", height: "100%" }}>
          <img
            src="https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=300&fit=crop"
            alt="Limited edition sneakers"
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
              borderRadius: "8px",
            }}
          />
          {/* Sale badge */}
          <div
            style={{
              position: "absolute",
              top: "8px",
              left: "8px",
              background: "#ff4444",
              color: "white",
              padding: "4px 8px",
              borderRadius: "4px",
              fontSize: "12px",
              fontWeight: "bold",
            }}
          >
            LIMITED
          </div>
          {/* Stock indicator */}
          <div
            style={{
              position: "absolute",
              bottom: "8px",
              right: "8px",
              background: "rgba(0,0,0,0.7)",
              color: "white",
              padding: "2px 6px",
              borderRadius: "12px",
              fontSize: "10px",
            }}
          >
            • 12 left
          </div>
        </div>
      )}
    />
  ),
}

/**
 * ProductCard with custom content rendering including star ratings and custom layouts.
 */
export const CustomContentRender: Story = {
  render: () => (
    <ProductCard
      title="Premium Coffee Maker"
      imageSrc="https://images.unsplash.com/photo-1517256064527-09c73fc73e38?w=300&h=300&fit=crop"
      imageAlt="Premium coffee maker"
      renderContent={({ title, price }) => (
        <div
          style={{
            padding: "12px",
            display: "flex",
            flexDirection: "column",
            gap: "8px",
          }}
        >
          {/* Title */}
          <h3
            style={{
              margin: 0,
              fontSize: "14px",
              fontWeight: "600",
              lineHeight: "1.4",
            }}
          >
            {title}
          </h3>

          {/* Star rating */}
          <div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
            <div style={{ display: "flex", gap: "1px" }}>
              {"★★★★☆".split("").map((star, i) => (
                <span
                  key={i}
                  style={{
                    color: i < 4 ? "#ffd700" : "#ddd",
                    fontSize: "12px",
                  }}
                >
                  {star}
                </span>
              ))}
            </div>
            <span style={{ fontSize: "11px", color: "#666" }}>
              (124 reviews)
            </span>
          </div>

          {/* Features list */}
          <ul
            style={{
              margin: 0,
              padding: 0,
              listStyle: "none",
              fontSize: "11px",
              color: "#666",
            }}
          >
            <li>• Programmable brewing</li>
            <li>• Built-in grinder</li>
            <li>• 12-cup capacity</li>
          </ul>

          {/* Price with custom styling */}
          <div
            style={{
              display: "flex",
              alignItems: "baseline",
              gap: "8px",
              marginTop: "4px",
            }}
          >
            <span
              style={{
                fontSize: "16px",
                fontWeight: "bold",
                color: "#2d5a27",
              }}
            >
              ฿8,999
            </span>
            <span
              style={{
                fontSize: "12px",
                color: "#999",
                textDecoration: "line-through",
              }}
            >
              ฿12,999
            </span>
          </div>
        </div>
      )}
    />
  ),
}

/**
 * ProductCard with custom footer rendering including multiple action buttons.
 */
export const CustomFooterRender: Story = {
  render: () => (
    <ProductCard
      title="Gaming Laptop"
      caption="High-performance gaming machine"
      imageSrc="https://images.unsplash.com/photo-1603302576837-37561b2e2302?w=300&h=300&fit=crop"
      imageAlt="Gaming laptop"
      price={{
        price: "45,999",
        currency: "฿",
      }}
      renderFooter={() => (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "8px",
          }}
        >
          {/* Primary action */}
          <button
            style={{
              width: "100%",
              padding: "8px 16px",
              background: "#007bff",
              color: "white",
              border: "none",
              borderRadius: "6px",
              fontSize: "14px",
              fontWeight: "500",
              cursor: "pointer",
            }}
          >
            🛒 Add to Cart
          </button>

          {/* Secondary actions */}
          <div style={{ display: "flex", gap: "8px" }}>
            <button
              style={{
                flex: 1,
                padding: "6px 12px",
                background: "transparent",
                color: "#666",
                border: "1px solid #ddd",
                borderRadius: "6px",
                fontSize: "12px",
                cursor: "pointer",
              }}
            >
              ❤️ Wishlist
            </button>
            <button
              style={{
                flex: 1,
                padding: "6px 12px",
                background: "transparent",
                color: "#666",
                border: "1px solid #ddd",
                borderRadius: "6px",
                fontSize: "12px",
                cursor: "pointer",
              }}
            >
              📋 Compare
            </button>
          </div>
        </div>
      )}
    />
  ),
}

/**
 * ProductCard with custom image overlay for badges, labels, and promotional content.
 */
export const CustomImageOverlay: Story = {
  render: () => (
    <ProductCard
      title="Wireless Gaming Controller"
      caption="Premium wireless gaming experience"
      imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop"
      imageAlt="Wireless gaming controller"
      price={{
        price: "2,299",
        discountPrice: "2,899",
        currency: "฿",
      }}
      renderImageOverlay={() => (
        <div
          style={{
            position: "absolute",
            top: "8px",
            left: "8px",
            borderRadius: "16px",
            fontSize: "11px",
            fontWeight: "bold",
            display: "flex",
            alignItems: "center",
            gap: "4px",
          }}
        >
          <ProductBadge variant="discount" label="20%" />
        </div>
      )}
    />
  ),
}

/**
 * ProductCard with multiple overlay variants showing different badge styles.
 */
export const OverlayVariants: Story = {
  render: () => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
        gap: "16px",
        padding: "16px",
        background: "#f8f9fa",
      }}
    >
      {/* Hot Deal Badge */}
      <ProductCard
        title="Smart TV 55'"
        imageSrc="https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=300&h=300&fit=crop"
        imageAlt="Smart TV"
        price={{ price: "15,999", currency: "฿" }}
        renderImageOverlay={() => (
          <div
            style={{
              position: "absolute",
              top: "8px",
              left: "8px",
              background: "#ff4757",
              color: "white",
              padding: "4px 12px",
              borderRadius: "20px",
              fontSize: "11px",
              fontWeight: "bold",
              textTransform: "uppercase",
            }}
          >
            🔥 Hot Deal
          </div>
        )}
      />

      {/* New Arrival Badge */}
      <ProductCard
        title="Smartphone Pro"
        imageSrc="https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=300&fit=crop"
        imageAlt="Smartphone"
        price={{ price: "25,900", currency: "฿" }}
        renderImageOverlay={() => (
          <div
            style={{
              position: "absolute",
              top: "8px",
              right: "8px",
              background: "#3742fa",
              color: "white",
              padding: "4px 10px",
              borderRadius: "4px",
              fontSize: "10px",
              fontWeight: "bold",
              textTransform: "uppercase",
            }}
          >
            ✨ New
          </div>
        )}
      />

      {/* Best Seller Badge */}
      <ProductCard
        title="Laptop Gaming"
        imageSrc="https://images.unsplash.com/photo-1525547719571-a2d4ac8945e2?w=300&h=300&fit=crop"
        imageAlt="Gaming laptop"
        price={{ price: "45,999", currency: "฿" }}
        renderImageOverlay={() => (
          <div
            style={{
              position: "absolute",
              top: "8px",
              left: "50%",
              transform: "translateX(-50%)",
              background: "#ffa502",
              color: "white",
              padding: "4px 12px",
              borderRadius: "0 0 12px 12px",
              fontSize: "10px",
              fontWeight: "bold",
              textTransform: "uppercase",
            }}
          >
            👑 Best Seller
          </div>
        )}
      />

      {/* Limited Edition Badge */}
      <ProductCard
        title="Headphones Pro"
        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop"
        imageAlt="Pro headphones"
        price={{ price: "8,999", currency: "฿" }}
        renderImageOverlay={() => (
          <div
            style={{
              position: "absolute",
              top: "8px",
              left: "8px",
              background: "linear-gradient(45deg, #667eea, #764ba2)",
              color: "white",
              padding: "4px 8px",
              borderRadius: "8px",
              fontSize: "9px",
              fontWeight: "bold",
              textTransform: "uppercase",
              border: "1px solid rgba(255,255,255,0.3)",
            }}
          >
            💎 Limited
          </div>
        )}
      />
    </div>
  ),
}
