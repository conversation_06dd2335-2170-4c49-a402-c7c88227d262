"use client"

import React, { useC<PERSON>back, useMemo, useState } from "react"
import { Button, Input, Typography } from "@apollo/ui"
import { CheckCircle, Search } from "@design-systems/apollo-icons"
import { Illustration } from "@design-systems/apollo-ui"

interface IllustrationCategory {
  name: string
  illustrations: string[]
  description: string
}

// Categorize illustrations based on their names and common usage patterns
const illustrationCategories: IllustrationCategory[] = [
  {
    name: "Empty States",
    description: "Illustrations for empty states and no content scenarios",
    illustrations: [
      "FileNotFound",
      "AddInformation",
    ],
  },
  {
    name: "Error States", 
    description: "Illustrations for error and problem scenarios",
    illustrations: [
      "FileNotFound",
    ],
  },
  {
    name: "Information",
    description: "Illustrations for informational and guidance scenarios",
    illustrations: [
      "AddInformation",
    ],
  },
]

export interface IllustrationGalleryComponentProps {
  showCategories?: boolean
  searchPlaceholder?: string
}

export function IllustrationGalleryComponent({
  showCategories = true,
  searchPlaceholder = "Search illustrations...",
}: IllustrationGalleryComponentProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("All")
  const [copiedIllustration, setCopiedIllustration] = useState<string | null>(null)

  // Get all available illustrations
  const allIllustrations = useMemo(() => Object.entries(Illustration), [])

  // Filter illustrations based on search term and category
  const filteredIllustrations = useMemo(() => {
    let filtered = allIllustrations

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(([name]) =>
        name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== "All") {
      const category = illustrationCategories.find(cat => cat.name === selectedCategory)
      if (category) {
        filtered = filtered.filter(([name]) => category.illustrations.includes(name))
      }
    }

    return filtered
  }, [allIllustrations, searchTerm, selectedCategory])

  const handleCopy = useCallback((illustrationName: string) => {
    const importStatement = `<Illustration.${illustrationName} />`
    navigator.clipboard.writeText(importStatement)

    setCopiedIllustration(illustrationName)
    setTimeout(() => {
      setCopiedIllustration(null)
    }, 2000)
  }, [])

  const categoryOptions = ["All", ...illustrationCategories.map((cat) => cat.name)]

  return (
    <div style={{ width: "100%" }}>
      {/* Search and Filter Controls */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "16px",
          marginBottom: "24px",
        }}
      >
        {/* Search Input */}
        <Input
          placeholder={searchPlaceholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          startDecorator={<Search size={20} />}
          style={{ maxWidth: "400px" }}
        />

        {/* Category Filter */}
        {showCategories && (
          <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
            {categoryOptions.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "filled" : "outline"}
                size="small"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Button>
            ))}
          </div>
        )}

        {/* Results Count */}
        <Typography level="bodySmall" style={{ color: "#6b7280" }}>
          {filteredIllustrations.length} illustration{filteredIllustrations.length !== 1 ? 's' : ''} found
        </Typography>
      </div>

      {/* Category Description */}
      {showCategories && selectedCategory !== "All" && (
        <div
          style={{
            padding: "12px 16px",
            backgroundColor: "#f8fafc",
            borderRadius: "8px",
            marginBottom: "16px",
            border: "1px solid #e2e8f0",
          }}
        >
          <Typography level="bodySmall" style={{ color: "#475569" }}>
            {illustrationCategories.find(cat => cat.name === selectedCategory)?.description}
          </Typography>
        </div>
      )}

      {/* Illustrations Grid */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fill, minmax(200px, 1fr))",
          gap: "16px",
          marginBottom: "24px",
        }}
      >
        {filteredIllustrations.map(([illustrationName, IllustrationComponent]) => (
          <button
            key={illustrationName}
            onClick={() => handleCopy(illustrationName)}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault()
                handleCopy(illustrationName)
              }
            }}
            aria-label={`Copy ${illustrationName} illustration import statement`}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              padding: "20px",
              border: "1px solid #e5e7eb",
              borderRadius: "12px",
              cursor: "pointer",
              position: "relative",
              transition: "all 0.2s ease",
              backgroundColor: "#ffffff",
              outline: "none",
              minHeight: "180px",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = "#f9fafb"
              e.currentTarget.style.borderColor = "#d1d5db"
              e.currentTarget.style.transform = "translateY(-2px)"
              e.currentTarget.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.1)"
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = "#ffffff"
              e.currentTarget.style.borderColor = "#e5e7eb"
              e.currentTarget.style.transform = "translateY(0)"
              e.currentTarget.style.boxShadow = "none"
            }}
            onFocus={(e) => {
              e.currentTarget.style.borderColor = "#3b82f6"
              e.currentTarget.style.boxShadow = "0 0 0 3px rgba(59, 130, 246, 0.1)"
            }}
            onBlur={(e) => {
              e.currentTarget.style.borderColor = "#e5e7eb"
              e.currentTarget.style.boxShadow = "none"
            }}
          >
            <div style={{ marginBottom: "12px", display: "flex", alignItems: "center", justifyContent: "center" }}>
              {React.createElement(IllustrationComponent as any, { 
                style: { width: 114, height: 114 }
              })}
            </div>
            <Typography
              level="bodySmall"
              style={{
                textAlign: "center",
                fontSize: "14px",
                lineHeight: "1.3",
                wordBreak: "break-word",
                fontWeight: "500",
              }}
            >
              {illustrationName}
            </Typography>
            {copiedIllustration === illustrationName && (
              <div
                style={{
                  position: "absolute",
                  top: "8px",
                  right: "8px",
                  display: "flex",
                  alignItems: "center",
                  gap: "4px",
                  padding: "6px 10px",
                  backgroundColor: "#10b981",
                  color: "white",
                  borderRadius: "6px",
                  fontSize: "12px",
                  fontWeight: "500",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
                }}
              >
                <CheckCircle size={14} />
                Copied
              </div>
            )}
          </button>
        ))}
      </div>

      {/* Empty State */}
      {filteredIllustrations.length === 0 && (
        <div
          style={{
            textAlign: "center",
            padding: "60px 20px",
            color: "#6b7280",
          }}
        >
          <Typography level="bodyLarge" style={{ marginBottom: "8px" }}>
            No illustrations found
          </Typography>
          <Typography level="bodySmall">
            Try adjusting your search term or selecting a different category
          </Typography>
        </div>
      )}
    </div>
  )
}
