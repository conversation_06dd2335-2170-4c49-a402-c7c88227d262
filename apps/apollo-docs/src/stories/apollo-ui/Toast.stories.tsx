import { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { <PERSON><PERSON>, But<PERSON>, useToast } from "@apollo/ui"
import { CheckCircle, File } from "@design-systems/apollo-icons"
import {
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * Toast displays brief notification messages to users. It supports different types,
 * positioning, auto-dismiss functionality, decorators, and can be managed programmatically
 * through the useToast hook.
 */
const meta: Meta = {
  title: "@apollo∕ui/Components/Feedback/Toast",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2637-6140&m=dev",
    },
    docs: {
      description: {
        component:
          "Toast displays brief notification messages to users. It supports different types, positioning, auto-dismiss functionality, decorators, and can be managed programmatically through the useToast hook.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { useToast } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="toast-props">Hook API</h2>
          <p>
            The useToast hook provides the following methods for managing
            toasts:
          </p>

          <h3>Methods</h3>
          <table
            style={{
              width: "100%",
              borderCollapse: "collapse",
              marginBottom: "24px",
            }}
          >
            <thead>
              <tr style={{ borderBottom: "2px solid #e0e0e0" }}>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px 8px",
                    fontWeight: "600",
                  }}
                >
                  Method
                </th>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px 8px",
                    fontWeight: "600",
                  }}
                >
                  Description
                </th>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px 8px",
                    fontWeight: "600",
                  }}
                >
                  Parameters
                </th>
              </tr>
            </thead>
            <tbody>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  add(options)
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Add a new toast notification
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  ToastOptions
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  update(id, options)
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Update an existing toast by ID
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  string, ToastOptions
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  close(id)
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Close a specific toast by ID
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  string
                </td>
              </tr>
              <tr>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  toasts
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Array of current active toasts
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  ToastObject[]
                </td>
              </tr>
            </tbody>
          </table>

          <h2 id="toast-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use appropriate toast types to convey the right message - success for confirmations, error for problems, warning for cautions, info for general information",
              "Keep toast messages concise - users should understand what happened and what to do next",
              "Use auto-dismiss for non-critical messages, but allow manual dismissal for important information",
              "Position toasts consistently within your application - typically top-right or bottom-right",
              "Use decorators sparingly - only when they add clear value to the user experience",
              "Provide meaningful titles and descriptions that help users understand the context",
            ]}
          />

          <h3>Toast Options Arguments</h3>
          <p>
            The <code>add()</code> and <code>update()</code> methods accept an
            options object with the following properties:
          </p>

          <table
            style={{
              width: "100%",
              borderCollapse: "collapse",
              marginBottom: "24px",
            }}
          >
            <thead>
              <tr style={{ borderBottom: "2px solid #e0e0e0" }}>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px 8px",
                    fontWeight: "600",
                    width: "25%",
                  }}
                >
                  Property
                </th>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px 8px",
                    fontWeight: "600",
                    width: "20%",
                  }}
                >
                  Type
                </th>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px 8px",
                    fontWeight: "600",
                    width: "15%",
                  }}
                >
                  Default
                </th>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px 8px",
                    fontWeight: "600",
                    width: "40%",
                  }}
                >
                  Description
                </th>
              </tr>
            </thead>
            <tbody>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  title
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  ReactNode
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  -
                </td>
                <td style={{ padding: "12px 8px" }}>
                  The main title of the toast (required)
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  description
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  ReactNode
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  -
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Optional description text
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  type
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  "success" | "information" | "warning" | "error"
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  "information"
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Toast type that determines color and icon
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  position
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  "top-left" | "top-center" | "top-right" | "bottom-left" |
                  "bottom-center" | "bottom-right"
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  "top-right"
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Screen position where the toast appears
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  timeout
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  number
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  5000
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Auto-dismiss timeout in milliseconds (0 = no auto-dismiss)
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  isClosable
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  boolean
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  true
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Whether to show close button
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  fullWidth
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  boolean
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  false
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Whether the toast should take full width
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  startDecorator
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  ReactNode
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  -
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Content before the title (e.g., icons)
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  endDecorator
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  ReactNode
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  -
                </td>
                <td style={{ padding: "12px 8px" }}>
                  Content after the description
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  onClose
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  () =&gt; void
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  -
                </td>
                <td style={{ padding: "12px 8px" }}>Custom close handler</td>
              </tr>
              <tr>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  className
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  string
                </td>
                <td
                  style={{
                    padding: "12px 8px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  -
                </td>
                <td style={{ padding: "12px 8px" }}>Custom CSS classes</td>
              </tr>
            </tbody>
          </table>

          <h2 id="toast-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Toast component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloToast-root",
                description: "Styles applied to the toast root container",
                usageNotes:
                  "Use for overall toast styling including background, border, and shadow",
              },
              {
                cssClassName: ".ApolloToast-alert",
                description:
                  "Styles applied to the alert component within the toast",
                usageNotes:
                  "Contains the actual alert styling and content layout",
              },
              {
                cssClassName: ".ApolloToast-wrapper",
                description: "Styles applied to the toast wrapper/viewport",
                usageNotes:
                  "Controls positioning and stacking of multiple toasts",
              },
            ]}
          />
          <h2 id="toast-examples">Examples</h2>
          <Stories title="" />
          <h2 id="toast-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 12,
                        width: 300,
                        padding: 20,
                      }}
                    >
                      <Alert title="Submission successful" description="Promotion confirmation created successfully" color="success" />
                    </div>
                  ),
                  description:
                    "Use clear, specific messages with title and description that tell users what happened and what to do next",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 12,
                        width: 300,
                      }}
                    >
                      <Alert title="Error" color="error" fullWidth/>
                    </div>
                  ),
                  description:
                    "Avoid vague messages that don't provide actionable information",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
}

export default meta
type Story = StoryObj

// --- BASIC EXAMPLES ---

/**
 * Default toast example showing the most common usage with different message types.
 * Each type has its own semantic meaning and visual styling.
 */
export const Default: Story = {
  render: () => {
    const DefaultExample = () => {
      const { add } = useToast()

      const showSuccess = () => {
        add({
          title: "Success!",
          description: "Your action was completed successfully.",
          type: "success",
        })
      }

      const showError = () => {
        add({
          title: "Error occurred",
          description: "Something went wrong. Please try again.",
          type: "error",
        })
      }

      const showWarning = () => {
        add({
          title: "Warning",
          description: "Please review your input before proceeding.",
          type: "warning",
        })
      }

      const showInfo = () => {
        add({
          title: "Information",
          description: "Here's some helpful information for you.",
          type: "information",
        })
      }

      return (
        <div style={{ display: "flex", gap: "12px", flexWrap: "wrap" }}>
          <Button onClick={showSuccess} variant="filled">
            Success
          </Button>
          <Button onClick={showError} variant="filled">
            Error
          </Button>
          <Button onClick={showWarning} variant="filled">
            Warning
          </Button>
          <Button onClick={showInfo} variant="filled">
            Info
          </Button>
        </div>
      )
    }

    return <DefaultExample />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Basic toast usage with different types. Each type conveys a specific semantic meaning through color and iconography. Success for confirmations, error for problems, warning for cautions, and info for general information.",
      },
    },
  },
}

// --- POSITIONING ---

/**
 * Toast positioned at the top-left corner of the screen.
 */
export const PositionTopLeft: Story = {
  render: () => {
    const TopLeftExample = () => {
      const { add } = useToast()

      const showTopLeftToast = () => {
        add({
          title: "Top Left Position",
          description:
            "This toast appears at the top-left corner of the screen.",
          type: "information",
          position: "top-left",
          timeout: 4000,
        })
      }

      return (
        <Button onClick={showTopLeftToast} variant="outline">
          Show Top Left Toast
        </Button>
      )
    }

    return <TopLeftExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the top-left corner of the screen.",
      },
    },
  },
}

/**
 * Toast positioned at the top-center of the screen.
 */
export const PositionTopCenter: Story = {
  render: () => {
    const TopCenterExample = () => {
      const { add } = useToast()

      const showTopCenterToast = () => {
        add({
          title: "Top Center Position",
          description: "This toast appears at the top-center of the screen.",
          type: "success",
          position: "top-center",
          timeout: 4000,
        })
      }

      return (
        <Button onClick={showTopCenterToast} variant="outline">
          Show Top Center Toast
        </Button>
      )
    }

    return <TopCenterExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the top-center of the screen.",
      },
    },
  },
}

/**
 * Toast positioned at the top-right corner of the screen (default position).
 */
export const PositionTopRight: Story = {
  render: () => {
    const TopRightExample = () => {
      const { add } = useToast()

      const showTopRightToast = () => {
        add({
          title: "Top Right Position",
          description:
            "This toast appears at the top-right corner of the screen.",
          type: "warning",
          position: "top-right",
          timeout: 4000,
        })
      }

      return (
        <Button onClick={showTopRightToast} variant="outline">
          Show Top Right Toast
        </Button>
      )
    }

    return <TopRightExample />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Toast positioned at the top-right corner of the screen. This is the default position.",
      },
    },
  },
}

/**
 * Toast positioned at the bottom-left corner of the screen.
 */
export const PositionBottomLeft: Story = {
  render: () => {
    const BottomLeftExample = () => {
      const { add } = useToast()

      const showBottomLeftToast = () => {
        add({
          title: "Bottom Left Position",
          description:
            "This toast appears at the bottom-left corner of the screen.",
          type: "error",
          position: "bottom-left",
          timeout: 4000,
        })
      }

      return (
        <Button onClick={showBottomLeftToast} variant="outline">
          Show Bottom Left Toast
        </Button>
      )
    }

    return <BottomLeftExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the bottom-left corner of the screen.",
      },
    },
  },
}

/**
 * Toast positioned at the bottom-center of the screen.
 */
export const PositionBottomCenter: Story = {
  render: () => {
    const BottomCenterExample = () => {
      const { add } = useToast()

      const showBottomCenterToast = () => {
        add({
          title: "Bottom Center Position",
          description: "This toast appears at the bottom-center of the screen.",
          type: "information",
          position: "bottom-center",
          timeout: 4000,
        })
      }

      return (
        <Button onClick={showBottomCenterToast} variant="outline">
          Show Bottom Center Toast
        </Button>
      )
    }

    return <BottomCenterExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the bottom-center of the screen.",
      },
    },
  },
}

/**
 * Toast positioned at the bottom-right corner of the screen.
 */
export const PositionBottomRight: Story = {
  render: () => {
    const BottomRightExample = () => {
      const { add } = useToast()

      const showBottomRightToast = () => {
        add({
          title: "Bottom Right Position",
          description:
            "This toast appears at the bottom-right corner of the screen.",
          type: "success",
          position: "bottom-right",
          timeout: 4000,
        })
      }

      return (
        <Button onClick={showBottomRightToast} variant="outline">
          Show Bottom Right Toast
        </Button>
      )
    }

    return <BottomRightExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the bottom-right corner of the screen.",
      },
    },
  },
}

// --- CONTENT & ACTIONS ---

/**
 * Toast with decorators and action buttons for enhanced functionality.
 * Use decorators for icons and actions for interactive elements.
 */
export const WithDecorators: Story = {
  render: () => {
    const DecoratorExample = () => {
      const { add } = useToast()

      const showWithStartDecorator = () => {
        add({
          title: "File uploaded",
          description: "Your document has been successfully uploaded.",
          type: "success",
          startDecorator: <File size={24} />,
        })
      }

      const showWithEndDecorator = () => {
        add({
          title: "Action required",
          description: "Please review the changes before proceeding.",
          type: "warning",
          endDecorator: (
            <Button variant="outline" size="small">
              Review
            </Button>
          ),
        })
      }

      return (
        <div
          style={{
            display: "flex",
            gap: "12px",
            flexDirection: "column",
            maxWidth: "300px",
          }}
        >
          <Button onClick={showWithStartDecorator} variant="outline">
            With Start Decorator
          </Button>
          <Button onClick={showWithEndDecorator} variant="outline">
            With End Decorator
          </Button>
        </div>
      )
    }

    return <DecoratorExample />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Start decorators are typically used for icons, end decorators for secondary actions",
      },
    },
  },
}

// --- MANAGEMENT ---

/**
 * Demonstrates programmatic toast management including updating existing toasts
 * and closing specific or all toasts.
 */
export const Management: Story = {
  render: () => {
    const ManagementExample = () => {
      const { add, update, close, toasts } = useToast()
      const [lastToastId, setLastToastId] = useState<string | null>(null)

      const addProgressToast = () => {
        const toastId = `progress-${Date.now()}`
        add({
          id: toastId,
          title: "Processing...",
          description: "Your request is being processed.",
          type: "information",
        })
        setLastToastId(toastId)
      }

      const updateToSuccess = () => {
        if (lastToastId) {
          update(lastToastId, {
            title: "Completed!",
            description: "Your request has been processed successfully.",
            type: "success",
          })
        }
      }

      const updateToError = () => {
        if (lastToastId) {
          update(lastToastId, {
            title: "Failed",
            description: "An error occurred while processing your request.",
            type: "error",
          })
        }
      }

      const closeLastToast = () => {
        if (lastToastId) {
          close(lastToastId)
          setLastToastId(null)
        }
      }

      const closeAllToasts = () => {
        toasts.forEach((toast) => close(toast.id))
        setLastToastId(null)
      }

      return (
        <div
          style={{
            display: "flex",
            gap: "12px",
            flexDirection: "column",
            maxWidth: "300px",
          }}
        >
          <Button onClick={addProgressToast} variant="filled">
            Add Progress Toast
          </Button>
          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              onClick={updateToSuccess}
              variant="outline"
              size="small"
              disabled={toasts.length === 0 || !lastToastId}
            >
              Update to Success
            </Button>
            <Button
              onClick={updateToError}
              variant="outline"
              size="small"
              disabled={toasts.length === 0 || !lastToastId}
            >
              Update to Error
            </Button>
          </div>
          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              onClick={closeLastToast}
              variant="outline"
              size="small"
              disabled={toasts.length === 0 || !lastToastId}
            >
              Close Last
            </Button>
            <Button
              onClick={closeAllToasts}
              variant="outline"
              size="small"
              disabled={toasts.length === 0}
            >
              Close All ({toasts.length})
            </Button>
          </div>
        </div>
      )
    }

    return <ManagementExample />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates toast management capabilities including updating existing toasts in real-time and programmatically closing specific or all toasts. Useful for progress indicators and batch operations.",
      },
    },
  },
}

// --- BEHAVIOR ---

/**
 * Demonstrates timeout configurations for automatic toast dismissal.
 * Toasts can auto-dismiss after a specified time or persist until manually closed.
 */
export const Timeout: Story = {
  render: () => {
    const TimeoutExample = () => {
      const { add } = useToast()

      const showQuickToast = () => {
        add({
          title: "Quick notification",
          description: "This toast will disappear in 1 second.",
          type: "information",
          timeout: 1000,
        })
      }

      const showMediumToast = () => {
        add({
          title: "Medium duration",
          description: "This toast will disappear in 3 seconds.",
          type: "success",
          timeout: 3000,
        })
      }

      const showPersistentToast = () => {
        add({
          title: "Persistent toast",
          description: "This toast won't auto-dismiss. Click the X to close.",
          type: "warning",
          timeout: 0, // 0 means no auto-dismiss
          onClose: () => console.log("Toast closed!"),
        })
      }

      return (
        <div
          style={{
            display: "flex",
            gap: "12px",
            flexDirection: "column",
            maxWidth: "300px",
          }}
        >
          <Button onClick={showQuickToast} variant="outline">
            Quick (1s)
          </Button>
          <Button onClick={showMediumToast} variant="outline">
            Medium (3s)
          </Button>
          <Button onClick={showPersistentToast} variant="outline">
            Persistent (No timeout)
          </Button>
        </div>
      )
    }

    return <TimeoutExample />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Different timeout configurations for automatic toast dismissal. Set timeout to 0 for persistent toasts that don't auto-dismiss and require manual closure.",
      },
    },
  },
}

/**
 * Demonstrates multiple toasts stacking and sequential appearance.
 * Shows how the toast system handles multiple notifications.
 */
export const MultipleToasts: Story = {
  render: () => {
    const MultipleExample = () => {
      const { add } = useToast()

      const showMultipleToasts = () => {
        // Task 1 - immediate
        add({
          title: "Task 1 Complete",
          description: "First task finished successfully.",
          type: "success",
        })

        // Task 2 - after 500ms
        setTimeout(() => {
          add({
            title: "Processing Task 2",
            description: "Second task is in progress.",
            type: "information",
          })
        }, 500)

        // Task 3 - after 1000ms
        setTimeout(() => {
          add({
            title: "Warning for Task 3",
            description: "Third task needs attention.",
            type: "warning",
          })
        }, 1000)

        // Task 4 - after 1500ms
        setTimeout(() => {
          add({
            title: "Task 4 Failed",
            description: "Fourth task encountered an error.",
            type: "error",
          })
        }, 1500)
      }

      const showSequentialToasts = () => {
        // Step 1
        add({
          title: "Step 1",
          description: "Initializing process...",
          type: "information",
        })

        // Step 2 after 1 second
        setTimeout(() => {
          add({
            title: "Step 2",
            description: "Processing data...",
            type: "information",
          })
        }, 1000)

        // Complete after 2 seconds
        setTimeout(() => {
          add({
            title: "Complete",
            description: "All steps completed successfully!",
            type: "success",
          })
        }, 2000)
      }

      return (
        <div
          style={{
            display: "flex",
            gap: "12px",
            flexDirection: "column",
            maxWidth: "300px",
          }}
        >
          <Button onClick={showMultipleToasts} variant="filled">
            Show Multiple Toasts
          </Button>
          <Button onClick={showSequentialToasts} variant="outline">
            Show Sequential Toasts
          </Button>
        </div>
      )
    }

    return <MultipleExample />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates how multiple toasts stack and appear sequentially. Useful for showing progress through multi-step processes or handling multiple simultaneous notifications.",
      },
    },
  },
}

/**
 * Demonstrates custom close behavior and closable configuration.
 * Shows how to handle close events and control toast dismissal.
 */
export const CloseBehavior: Story = {
  render: () => {
    const CloseExample = () => {
      const { add } = useToast()

      const showClosableToast = () => {
        add({
          title: "Closable Toast",
          description: "This toast can be closed by clicking the X button.",
          type: "information",
          isClosable: true,
        })
      }

      const showNonClosableToast = () => {
        add({
          title: "Non-closable Toast",
          description:
            "This toast cannot be closed manually and will auto-dismiss.",
          type: "warning",
          isClosable: false,
          timeout: 5000,
        })
      }

      const showWithCustomClose = () => {
        add({
          title: "Custom Close Handler",
          description: "This toast has a custom close behavior.",
          type: "success",
          onClose: () => {
            console.log("Toast closed with custom handler!")
            alert("Custom close logic executed!")
          },
        })
      }

      return (
        <div
          style={{
            display: "flex",
            gap: "12px",
            flexDirection: "column",
            maxWidth: "300px",
          }}
        >
          <Button onClick={showClosableToast} variant="outline">
            Closable Toast
          </Button>
          <Button onClick={showNonClosableToast} variant="outline">
            Non-closable Toast
          </Button>
          <Button onClick={showWithCustomClose} variant="outline">
            Custom Close Handler
          </Button>
        </div>
      )
    }

    return <CloseExample />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates the isClosable property and custom close handlers. When isClosable is true, users can manually close the toast. When false, the toast can only be dismissed automatically via timeout.",
      },
    },
  },
}

// --- CUSTOMIZATION ---

/**
 * Toast with full width configuration that spans the available container space.
 */
export const FullWidth: Story = {
  render: () => {
    const FullWidthExample = () => {
      const { add } = useToast()

      const showFullWidthToast = () => {
        add({
          title: "Full Width Toast",
          description:
            "This toast uses the fullWidth prop to span the entire available width of its container. This is useful for important notifications that need maximum visibility.",
          type: "success",
          fullWidth: true,
          timeout: 6000,
        })
      }

      return (
        <Button onClick={showFullWidthToast} variant="outline">
          Show Full Width Toast
        </Button>
      )
    }

    return <FullWidthExample />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates the fullWidth prop which makes the toast span the entire available width of its container. This is useful for important notifications that need maximum visibility and space for longer content.",
      },
    },
  },
}

/**
 * Toast with custom width using inline styles for precise width control.
 */
export const CustomWidth: Story = {
  render: () => {
    const CustomWidthExample = () => {
      const { add } = useToast()

      const showNarrowToast = () => {
        add({
          title: "Narrow Toast",
          description: "This toast has a custom narrow width of 250px.",
          type: "information",
          style: {
            width: "250px",
          },
          timeout: 5000,
        })
      }

      const showWideToast = () => {
        add({
          title: "Wide Custom Toast",
          description:
            "This toast has a custom wide width of 500px for displaying more detailed information and longer content.",
          type: "warning",
          style: {
            width: "500px",
          },
          timeout: 5000,
        })
      }

      const showExtraWideToast = () => {
        add({
          title: "Extra Wide Toast",
          description:
            "This toast demonstrates an extra wide custom width of 600px, which can be useful for displaying complex information, multiple lines of text, or when you need to accommodate longer translations in internationalized applications.",
          type: "error",
          style: {
            width: "600px",
            maxWidth: "90vw", // Responsive fallback
          },
          timeout: 7000,
        })
      }

      return (
        <div
          style={{
            display: "flex",
            gap: "12px",
            flexDirection: "column",
            maxWidth: "300px",
          }}
        >
          <Button onClick={showNarrowToast} variant="outline" size="small">
            Narrow (250px)
          </Button>
          <Button onClick={showWideToast} variant="outline" size="small">
            Wide (500px)
          </Button>
          <Button onClick={showExtraWideToast} variant="outline" size="small">
            Extra Wide (600px)
          </Button>
        </div>
      )
    }

    return <CustomWidthExample />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates custom width control using inline styles. You can set precise widths for different use cases - narrow for simple messages, wide for detailed information, or extra wide for complex content. Always consider responsive design by adding maxWidth constraints.",
      },
    },
  },
}

// --- REAL-WORLD EXAMPLES ---

/**
 * Real-world examples of toast usage in common application scenarios.
 * Demonstrates practical implementations for typical use cases.
 */
export const RealWorldExamples: Story = {
  render: () => {
    const RealWorldExample = () => {
      const { add } = useToast()

      const simulateFileUpload = () => {
        add({
          title: "Uploading file...",
          description: "document.pdf",
          type: "information",
          startDecorator: (
            <span>
              <File />
            </span>
          ),
        })

        setTimeout(() => {
          add({
            title: "Upload complete",
            description: "document.pdf has been uploaded successfully.",
            type: "success",
            startDecorator: (
              <span>
                <CheckCircle />
              </span>
            ),
            endDecorator: (
              <Button variant="outline" size="small">
                View
              </Button>
            ),
          })
        }, 2000)
      }

      const simulateFormValidation = () => {
        add({
          title: "Validation Error",
          description: "Please fill in all required fields before submitting.",
          type: "error",
        })
      }

      const simulateNetworkError = () => {
        add({
          title: "Connection Lost",
          description:
            "Unable to connect to server. Please check your internet connection.",
          type: "error",
        })
      }

      const simulateSuccessfulSave = () => {
        add({
          title: "Changes saved",
          description: "Your changes have been saved automatically.",
          type: "success",
        })
      }

      return (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: "12px",
            maxWidth: "500px",
          }}
        >
          <Button onClick={simulateFileUpload} variant="outline">
            File Upload Progress
          </Button>
          <Button onClick={simulateFormValidation} variant="outline">
            Form Validation Error
          </Button>
          <Button onClick={simulateNetworkError} variant="outline">
            Network Error
          </Button>
          <Button onClick={simulateSuccessfulSave} variant="outline">
            Auto Save Success
          </Button>
        </div>
      )
    }

    return <RealWorldExample />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Real-world examples of toast usage in common application scenarios including file uploads, form validation, network errors, and auto-save notifications. These examples demonstrate practical implementations with appropriate decorators and actions.",
      },
    },
  },
}
