import { Meta } from "@storybook/addon-docs/blocks"


<Meta title="@apollo∕ui/Theming/CSS Layers" tags={["docs"]} />

# CSS Layers

Apollo uses CSS `@layer` to manage the cascade order of styles and CSS variables. This powerful feature ensures predictable styling behavior across different theme versions and components.

**💡 Why CSS Layers?** 
> CSS Layers provide explicit control over style precedence, making it easier to manage complex theming scenarios and prevent unexpected style conflicts.

## 🏗️ Layer Architecture

Apollo organizes styles into **four distinct layers**, each serving a specific purpose in the theming system:

<div style={{
  background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
  borderRadius: "12px",
  padding: "24px",
  margin: "20px 0",
  border: "1px solid #e2e8f0"
}}>
  <table style={{
    width: "100%",
    borderCollapse: "collapse",
    background: "white",
    borderRadius: "8px",
    overflow: "hidden",
    boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
  }}>
    <thead>
      <tr style={{
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        color: "white"
      }}>
        <th style={{
          padding: "16px 20px",
          textAlign: "left",
          fontWeight: "600",
          border: "none"
        }}>Layer Name</th>
        <th style={{
          padding: "16px 20px",
          textAlign: "left",
          fontWeight: "600",
          border: "none"
        }}>Purpose</th>
      </tr>
    </thead>
    <tbody>
      <tr style={{ borderBottom: "1px solid #f1f5f9" }}>
        <td style={{ padding: "16px 20px", border: "none" }}>
          <code style={{
            background: "#f1f5f9",
            padding: "4px 8px",
            borderRadius: "4px",
            color: "#475569",
            fontWeight: "500"
          }}>reset</code>
        </td>
        <td style={{ padding: "16px 20px", border: "none", color: "#475569" }}>
          Global reset styles for consistent rendering across browsers
        </td>
      </tr>
      <tr style={{ borderBottom: "1px solid #f1f5f9" }}>
        <td style={{ padding: "16px 20px", border: "none" }}>
          <code style={{
            background: "#f1f5f9",
            padding: "4px 8px",
            borderRadius: "4px",
            color: "#475569",
            fontWeight: "500"
          }}>base</code>
        </td>
        <td style={{ padding: "16px 20px", border: "none", color: "#475569" }}>
          Base typography, layout styles, and foundational CSS
        </td>
      </tr>
      <tr>
        <td style={{ padding: "16px 20px", border: "none" }}>
          <code style={{
            background: "#fef3c7",
            padding: "4px 8px",
            borderRadius: "4px",
            color: "#92400e",
            fontWeight: "500"
          }}>legacy</code>
        </td>
        <td style={{ padding: "16px 20px", border: "none", color: "#475569" }}>
          <strong style={{ color: "#92400e" }}>Theme (Legacy)</strong> – Previous design system tokens and components
        </td>
      </tr>
      <tr style={{ borderBottom: "1px solid #f1f5f9" }}>
        <td style={{ padding: "16px 20px", border: "none" }}>
          <code style={{
            background: "#dbeafe",
            padding: "4px 8px",
            borderRadius: "4px",
            color: "#1e40af",
            fontWeight: "500"
          }}>apollo</code>
        </td>
        <td style={{ padding: "16px 20px", border: "none", color: "#475569" }}>
          <strong style={{ color: "#1e40af" }}>Theme version 2.0 (Apollo)</strong> – Modern design tokens and components
        </td>
      </tr>
    </tbody>
  </table>
</div>

**⚠️ Important:** Layer order determines precedence – styles in later layers override earlier ones when selectors have equal specificity.

## 🚀 Using Theme version 2.0 (Apollo)

The **recommended approach** for new projects. Apollo v2.0 provides modern design tokens and enhanced theming capabilities.

<div style={{
  background: "linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)",
  borderRadius: "12px",
  padding: "20px",
  margin: "16px 0",
  borderLeft: "4px solid #3b82f6"
}}>
  <h4 style={{
    margin: "0 0 12px 0",
    color: "#1e40af",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>📋</span> Default Layer Order
  </h4>

```css
@layer reset, base, legacy, apollo;
```

  <div style={{ marginTop: "16px" }}>
    <h5 style={{ margin: "0 0 8px 0", color: "#1e40af" }}>Cascade Order:</h5>
    <ol style={{ margin: "0", paddingLeft: "20px", color: "#1e40af" }}>
      <li><code style={{
        background: "rgba(59, 130, 246, 0.1)",
        padding: "2px 6px",
        borderRadius: "3px"
      }}>reset</code> – Browser resets</li>
      <li><code style={{
        background: "rgba(59, 130, 246, 0.1)",
        padding: "2px 6px",
        borderRadius: "3px"
      }}>base</code> – Foundation styles</li>
      <li><code style={{
        background: "rgba(59, 130, 246, 0.1)",
        padding: "2px 6px",
        borderRadius: "3px"
      }}>legacy</code> – Legacy theme styles</li>
      <li><code style={{
        background: "rgba(59, 130, 246, 0.1)",
        padding: "2px 6px",
        borderRadius: "3px"
      }}>apollo</code> – <strong>Apollo v2.0 styles (takes precedence)</strong></li>
    </ol>
  </div>
</div>

## 🔄 Using Theme (Legacy)

For projects that need to maintain compatibility with the previous design system.

<div style={{
  background: "linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)",
  borderRadius: "12px",
  padding: "20px",
  margin: "16px 0",
  borderLeft: "4px solid #f59e0b"
}}>
  <h4 style={{
    margin: "0 0 12px 0",
    color: "#92400e",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>⚙️</span> Legacy Layer Configuration
  </h4>

  <p style={{ margin: "0 0 12px 0", color: "#92400e" }}>
    Place <code style={{
      background: "rgba(245, 158, 11, 0.2)",
      padding: "2px 6px",
      borderRadius: "3px"
    }}>legacy</code> layer <strong>after</strong> <code style={{
      background: "rgba(245, 158, 11, 0.2)",
      padding: "2px 6px",
      borderRadius: "3px"
    }}>apollo</code> to ensure legacy styles take precedence:
  </p>

```css
@layer reset, base, apollo, legacy;
```

  <div style={{ marginTop: "16px" }}>
    <h5 style={{ margin: "0 0 8px 0", color: "#92400e" }}>Cascade Order:</h5>
    <ol style={{ margin: "0", paddingLeft: "20px", color: "#92400e" }}>
      <li><code style={{
        background: "rgba(245, 158, 11, 0.1)",
        padding: "2px 6px",
        borderRadius: "3px"
      }}>reset</code> – Browser resets</li>
      <li><code style={{
        background: "rgba(245, 158, 11, 0.1)",
        padding: "2px 6px",
        borderRadius: "3px"
      }}>base</code> – Foundation styles</li>
      <li><code style={{
        background: "rgba(245, 158, 11, 0.1)",
        padding: "2px 6px",
        borderRadius: "3px"
      }}>apollo</code> – Apollo v2.0 styles (for unchanged components)</li>
      <li><code style={{
        background: "rgba(245, 158, 11, 0.1)",
        padding: "2px 6px",
        borderRadius: "3px"
      }}>legacy</code> – <strong>Legacy overrides (takes precedence)</strong></li>
    </ol>
  </div>
</div>

## ⚠️ Mixing Themes (Not Recommended)

<div style={{
  background: "linear-gradient(135deg, #fee2e2 0%, #fecaca 100%)",
  borderRadius: "12px",
  padding: "20px",
  margin: "16px 0",
  borderLeft: "4px solid #ef4444"
}}>
  <h4 style={{
    margin: "0 0 12px 0",
    color: "#dc2626",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>🚨</span> Caution: Theme Mixing
  </h4>
  <p style={{ margin: "0", color: "#dc2626" }}>
    While technically possible to load both <code style={{
      background: "rgba(239, 68, 68, 0.1)",
      padding: "2px 6px",
      borderRadius: "3px"
    }}>apollo</code> and <code style={{
      background: "rgba(239, 68, 68, 0.1)",
      padding: "2px 6px",
      borderRadius: "3px"
    }}>legacy</code> layers, this can lead to inconsistent styles and unexpected behavior. If you must mix themes, <strong>always place <code style={{
      background: "rgba(239, 68, 68, 0.1)",
      padding: "2px 6px",
      borderRadius: "3px"
    }}>legacy</code> last</strong> in the layer order.
  </p>
</div>

## 📚 Quick Reference

<div style={{
  background: "linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)",
  borderRadius: "12px",
  padding: "24px",
  margin: "20px 0",
  border: "1px solid #bbf7d0"
}}>
  <table style={{
    width: "100%",
    borderCollapse: "collapse",
    background: "white",
    borderRadius: "8px",
    overflow: "hidden",
    boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
  }}>
    <thead>
      <tr style={{
        background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
        color: "white"
      }}>
        <th style={{
          padding: "16px 20px",
          textAlign: "left",
          fontWeight: "600",
          border: "none"
        }}>Goal</th>
        <th style={{
          padding: "16px 20px",
          textAlign: "left",
          fontWeight: "600",
          border: "none"
        }}>Layer Configuration</th>
      </tr>
    </thead>
    <tbody>
      <tr style={{ borderBottom: "1px solid #f1f5f9" }}>
        <td style={{
          padding: "16px 20px",
          border: "none",
          color: "#374151",
          fontWeight: "500"
        }}>
          Use only Theme version 2.0 (Apollo)
        </td>
        <td style={{ padding: "16px 20px", border: "none" }}>
          <code style={{
            background: "#f0fdf4",
            padding: "6px 12px",
            borderRadius: "6px",
            color: "#166534",
            fontWeight: "500",
            fontSize: "14px"
          }}>@layer reset, base, apollo;</code>
        </td>
      </tr>
      <tr style={{ borderBottom: "1px solid #f1f5f9" }}>
        <td style={{
          padding: "16px 20px",
          border: "none",
          color: "#374151",
          fontWeight: "500"
        }}>
          Use Theme (Legacy)
        </td>
        <td style={{ padding: "16px 20px", border: "none" }}>
          <code style={{
            background: "#fef3c7",
            padding: "6px 12px",
            borderRadius: "6px",
            color: "#92400e",
            fontWeight: "500",
            fontSize: "14px"
          }}>@layer reset, base, apollo, legacy;</code>
        </td>
      </tr>
      <tr>
        <td style={{
          padding: "16px 20px",
          border: "none",
          color: "#374151",
          fontWeight: "500"
        }}>
          Mix Legacy overrides on Apollo
        </td>
        <td style={{ padding: "16px 20px", border: "none" }}>
          <code style={{
            background: "#fee2e2",
            padding: "6px 12px",
            borderRadius: "6px",
            color: "#dc2626",
            fontWeight: "500",
            fontSize: "14px"
          }}>@layer reset, base, apollo, legacy;</code>
        </td>
      </tr>
    </tbody>
  </table>
</div>


## 💡 Best Practices & Notes

<div style={{
  background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
  borderRadius: "12px",
  padding: "24px",
  margin: "20px 0",
  border: "1px solid #cbd5e1"
}}>
  <div style={{ display: "grid", gap: "16px" }}>

    <div style={{
      background: "white",
      padding: "16px",
      borderRadius: "8px",
      borderLeft: "4px solid #3b82f6"
    }}>
      <h4 style={{
        margin: "0 0 8px 0",
        color: "#1e40af",
        display: "flex",
        alignItems: "center"
      }}>
        <span style={{ marginRight: "8px" }}>🎯</span> Layer Order is Critical
      </h4>
      <p style={{ margin: "0", color: "#475569" }}>
        CSS from later layers always overrides earlier layers when selectors have equal specificity. This predictable behavior is the foundation of Apollo's theming system.
      </p>
    </div>

    <div style={{
      background: "white",
      padding: "16px",
      borderRadius: "8px",
      borderLeft: "4px solid #10b981"
    }}>
      <h4 style={{
        margin: "0 0 8px 0",
        color: "#059669",
        display: "flex",
        alignItems: "center"
      }}>
        <span style={{ marginRight: "8px" }}>⚡</span> Omitted Layers
      </h4>
      <p style={{ margin: "0", color: "#475569" }}>
        If you omit a layer from your <code style={{
          background: "#f1f5f9",
          padding: "2px 6px",
          borderRadius: "3px"
        }}>@layer</code> declaration, its styles will still load but may have lower priority than declared layers.
      </p>
    </div>

    <div style={{
      background: "white",
      padding: "16px",
      borderRadius: "8px",
      borderLeft: "4px solid #8b5cf6"
    }}>
      <h4 style={{
        margin: "0 0 8px 0",
        color: "#7c3aed",
        display: "flex",
        alignItems: "center"
      }}>
        <span style={{ marginRight: "8px" }}>🚀</span> Future-Ready Foundation
      </h4>
      <p style={{ margin: "0", color: "#475569" }}>
        The <code style={{
          background: "#f1f5f9",
          padding: "2px 6px",
          borderRadius: "3px"
        }}>apollo</code> layer contains the most recent design tokens and components – it serves as the foundation for all future updates and enhancements.
      </p>
    </div>

  </div>
</div>

---

<div style={{
  textAlign: "center",
  padding: "20px",
  color: "#64748b",
  fontStyle: "italic"
}}>
  <p style={{ margin: "0" }}>
    For more information about CSS Layers, visit the
    <a
      href="https://developer.mozilla.org/en-US/docs/Web/CSS/@layer"
      target="_blank"
      style={{ color: "#3b82f6", textDecoration: "none" }}
    >
      MDN documentation
    </a>
  </p>
</div>