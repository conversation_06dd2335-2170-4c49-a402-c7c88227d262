import React, { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { Chip, Typography } from "@apollo/ui"
import { Search } from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * Chip component
 *
 * The Chip component is a compact element that represents an input, attribute, or action.
 * It allows users to enter information, make selections, filter content, or trigger actions.
 *
 * Notes:
 * - Default variant is "filled";
 * - Default size is "medium";
 * - Default color is "primary";
 * - Available variants: "filled" | "outline";
 * - Available sizes: "large" | "medium" | "small";
 * - Available colors: "primary" | "negative".
 */
const meta = {
  title: "@apollo∕ui/Components/Data Display/Chip",
  component: Chip,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2505-25034&m=dev",
    },
    docs: {
      description: {
        component:
          "The Chip component renders a compact element with Apollo design system styling. Supports multiple variants, sizes, colors, and interactive functionality like close and check actions.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Chip } from "@apollo/ui"`} language="tsx" />
          <h2 id="chip-props">Props</h2>
          <ArgTypes />
          <h2 id="chip-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Chips represent a complex piece of information in compact form, such as an entity (person, place, or thing) or text. They enable user input and verify that input by converting text into chips.",
              "Use clear, descriptive labels that represent the content or action",
              "Use chips for tags, filters, categories, or selected items",
              "Use the close action (onClose) for removable items like filters or tags",
              "Use the check action (onCheck) for selectable items or toggles",
              "Keep chip labels concise - avoid long text that might wrap",
              "Use 8px spacing between chips"
            ]}
          />
          <h2 id="chip-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Provide an id for the Chips to enable proper accessibility associations and unique identification.
              </>,
              <>
                Use the <code>disabled</code> prop to disable chips that are
                not currently actionable, ensuring they are not focusable.
              </>,
              <>
                When using interactive chips with <code>onClose</code> or{" "}
                <code>onCheck</code>, ensure the action is clear from context.
              </>,
              <>
                Use appropriate color variants to convey meaning - negative for
                error states or destructive actions.
              </>,
              <>
                Group related chips logically and provide clear section headings
                when displaying multiple chip categories.
              </>,
            ]}
          />
          <h2 id="chip-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Chip component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloChip-root",
                description: "Styles applied to the root chip element",
                usageNotes: "Use for overall chip styling, positioning, and base properties",
              },
              {
                cssClassName: ".ApolloChip-label",
                description: "Styles applied to the label text element",
                usageNotes: "Use for customizing text styling within the chip",
              },
              {
                cssClassName: ".ApolloChip-closeIcon",
                description: "Styles applied to the close button icon",
                usageNotes: "Use for customizing the close button appearance",
              },
              {
                cssClassName: ".ApolloChip-checkIcon",
                description: "Styles applied to the check button icon",
                usageNotes: "Use for customizing the check button appearance",
              },
            ]}
          />
          <h2 id="chip-examples">Examples</h2>
          <Stories title="" />
          <h2 id="chip-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                      <Chip label="JavaScript" />
                      <Chip label="React" />
                      <Chip label="TypeScript" />
                    </div>
                  ),
                  description: "Use clear, descriptive labels for categories or tags",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 8, flexWrap: "wrap", maxWidth: "100px" }}>
                      <Chip label="Item 1" />
                      <Chip label="Thing" />
                      <Chip label="Mandarin Chinese" />
                    </div>
                  ),
                  description:
                    "Avoid generic labels that don't provide meaningful information and long text that might wrap or truncate",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                      <Chip label="Active Filter" onClose={() => {}} />
                      <Chip label="Selected Tag" onClose={() => {}} />
                    </div>
                  ),
                  description:
                    "Use close action for removable items like filters and tags",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                      <Chip label="Permanent Label" onClose={() => {}} />
                      <Chip label="Static Info" onClose={() => {}} />
                    </div>
                  ),
                  description:
                    "Don't add close actions to permanent or informational chips",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                      <Chip label="Error" color="negative" />
                      <Chip label="Warning" color="primary" />
                      <Chip label="Success" color="primary" />
                    </div>
                  ),
                  description:
                    "Use appropriate colors to convey meaning and status",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                      <Chip label="Success" color="negative" />
                      <Chip label="Error" color="primary" />
                    </div>
                  ),
                  description:
                    "Don't use colors that conflict with the chip's meaning or status",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    label: {
      control: { type: "text" },
      description: "The text content displayed in the chip.",
      table: { type: { summary: "string" } },
    },
    variant: {
      control: { type: "radio" },
      options: ["filled", "outline"],
      description: "Visual style variant of the chip. Default is 'filled'.",
      table: {
        type: { summary: '"filled" | "outline"' },
        defaultValue: { summary: "filled" },
      },
    },
    size: {
      control: { type: "radio" },
      options: ["large", "medium", "small"],
      description: "Visual size of the chip. Default is 'medium'.",
      table: {
        type: { summary: '"large" | "medium" | "small"' },
        defaultValue: { summary: "medium" },
      },
    },
    color: {
      control: { type: "radio" },
      options: ["primary", "negative"],
      description: "Color theme of the chip. Default is 'primary'.",
      table: {
        type: { summary: '"primary" | "negative"' },
        defaultValue: { summary: "primary" },
      },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disables the chip and all its interactive elements.",
    },
    onClose: {
      control: false,
      description: "Callback fired when the close button is clicked.",
      table: {
        type: {
          summary: "(event: React.MouseEvent<HTMLButtonElement>) => void",
        },
      },
    },
    onCheck: {
      control: false,
      description: "Callback fired when the check button is clicked.",
      table: {
        type: {
          summary: "(event: React.MouseEvent<HTMLButtonElement>) => void",
        },
      },
    },
    onClick: {
      control: false,
      description: "Callback fired when the chip is clicked.",
      table: {
        type: {
          summary: "(event: React.MouseEvent<HTMLDivElement>) => void",
        },
      },
    },
    ref: {
      control: false,
      description: "Ref for the underlying div element.",
      table: { type: { summary: "React.Ref<HTMLDivElement>" } },
    },
  },
  args: {
    label: "Chip",
    variant: "filled",
    size: "medium",
    color: "primary",
    disabled: false,
  },
} satisfies Meta<typeof Chip>

export default meta

type Story = StoryObj<typeof Chip>

/** Default Chip (demonstrates default filled variant) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Chip with default settings. The component defaults to variant 'filled', size 'medium', and color 'primary'.",
      },
    },
  },
  args: {
    label: "Chip",
  },
}

/** Chip with different variants (filled, outline) */
export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases all available chip variants: filled (default) and outline.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <Chip {...args} variant="filled" label="Filled" />
      <Chip {...args} variant="outline" label="Outline" />
    </div>
  ),
}

/** Chip with different sizes (large, medium, small) */
export const Sizes: Story = {
  parameters: {
    docs: {
      description: {
        story: "Showcases all available sizes: large, medium (default), and small.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <Chip {...args} size="large" label="Large" />
      <Chip {...args} size="medium" label="Medium" />
      <Chip {...args} size="small" label="Small" />
    </div>
  ),
}

/** Chip with different colors (primary, negative) */
export const Colors: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases both available color themes: primary (default) and negative.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <Chip {...args} color="primary" label="Primary" />
      <Chip {...args} color="negative" label="Negative" />
    </div>
  ),
}

/** Chip with interactive actions (close and check) */
export const InteractiveActions: Story = {
  parameters: {
    docs: {
      description: {
        story: "Chips with interactive close and check actions.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Chip
        {...args}
        label="Closeable"
        onClose={() => alert("Chip closed!")}
      />
      <Chip
        {...args}
        label="Checkable"
        onCheck={() => alert("Chip checked!")}
      />
      <Chip
        {...args}
        label="Both Actions"
        onClose={() => alert("Chip closed!")}
        onCheck={() => alert("Chip checked!")}
      />
      <Chip
        {...args}
        variant="outline"
        label="Outline Closeable"
        onClose={() => alert("Chip closed!")}
      />
      <Chip
        {...args}
        variant="outline"
        label="Outline Checkable"
        onCheck={() => alert("Chip checked!")}
      />
    </div>
  ),
}

/** Chip disabled states */
export const Disabled: Story = {
  parameters: {
    docs: {
      description: {
        story: "Chips in disabled state across all variants and colors.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Chip disabled label="Disabled Filled" />
      <Chip disabled variant="outline" label="Disabled Outline" />
      <Chip disabled color="negative" label="Disabled Negative" />
      <Chip disabled label="Disabled with Close" onClose={() => {}} />
      <Chip disabled label="Disabled with Check" onCheck={() => {}} />
    </div>
  ),
}

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of chip states including default, disabled states across different variants and colors.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 20,
          alignItems: "start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Filled Chips</Typography>
          <Chip label="Default" />
          <Chip disabled label="Disabled Default" />
          <Chip color="negative" label="Negative" />
          <Chip color="negative" disabled label="Disabled Negative" />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Outline Chips</Typography>
          <Chip variant="outline" label="Default" />
          <Chip variant="outline" disabled label="Disabled Default" />
          <Chip variant="outline" color="negative" label="Negative" />
          <Chip variant="outline" color="negative" disabled label="Disabled Negative" />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">With Close Action</Typography>
          <Chip label="Closeable" onClose={() => {}} />
          <Chip label="Disabled Close" onClose={() => {}} disabled />
          <Chip variant="outline" label="Outline Close" onClose={() => {}} />
          <Chip variant="outline" label="Disabled Outline" onClose={() => {}} disabled />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">With Check Action</Typography>
          <Chip label="Checkable" onCheck={() => {}} />
          <Chip label="Disabled Check" onCheck={() => {}} disabled />
          <Chip variant="outline" label="Outline Check" onCheck={() => {}} />
          <Chip variant="outline" label="Disabled Outline" onCheck={() => {}} disabled />
        </div>
      </div>
    )
  },
}

/** Clickable chips with onClick handler */
export const Clickable: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Chips with onClick handlers for general interaction, useful for navigation or selection.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Chip
        label="Click Me"
        onClick={() => alert("Chip clicked!")}
      />
      <Chip
        variant="outline"
        label="Navigate"
        onClick={() => alert("Navigation clicked!")}
      />
      <Chip
        label="Select Category"
        onClick={() => alert("Category selected!")}
      />
      <Chip
        color="negative"
        label="Remove Item"
        onClick={() => alert("Item removed!")}
      />
    </div>
  ),
}

/** Real-world filter example */
export const FilterExample: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "Interactive filter chips example showing how chips can be used for filtering content.",
      },
    },
  },
  render: () => {
    function FilterDemo() {
      const [selectedFilters, setSelectedFilters] = useState<string[]>([
        "javascript",
        "react",
      ])

      const filterOptions = [
        { id: "javascript", label: "JavaScript", color: "primary" as const },
        { id: "react", label: "React", color: "primary" as const },
        { id: "typescript", label: "TypeScript", color: "primary" as const },
        { id: "vue", label: "Vue.js", color: "primary" as const },
        { id: "angular", label: "Angular", color: "primary" as const },
        { id: "node", label: "Node.js", color: "primary" as const },
      ]

      const handleFilterRemove = (filterId: string) => {
        setSelectedFilters(prev => prev.filter(id => id !== filterId))
      }

      const handleFilterAdd = (filterId: string) => {
        setSelectedFilters(prev => [...prev, filterId])
      }

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
              Active Filters:
            </Typography>
            <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
              {selectedFilters.length === 0 ? (
                <Typography level="bodyMedium" style={{ color: "#666" }}>
                  No filters selected
                </Typography>
              ) : (
                selectedFilters.map(filterId => {
                  const filter = filterOptions.find(f => f.id === filterId)
                  return filter ? (
                    <Chip
                      key={filterId}
                      label={filter.label}
                      color={filter.color}
                      onClose={() => handleFilterRemove(filterId)}
                    />
                  ) : null
                })
              )}
            </div>
          </div>

          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
              Available Filters:
            </Typography>
            <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
              {filterOptions
                .filter(filter => !selectedFilters.includes(filter.id))
                .map(filter => (
                  <Chip
                    key={filter.id}
                    label={filter.label}
                    variant="outline"
                    color={filter.color}
                    onClick={() => handleFilterAdd(filter.id)}
                  />
                ))}
            </div>
          </div>
        </div>
      )
    }
    return <FilterDemo />
  },
}

/** Tag management example */
export const TagManagement: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "Interactive tag management example showing how chips can be used for adding and removing tags.",
      },
    },
  },
  render: () => {
    function TagDemo() {
      const [tags, setTags] = useState<string[]>(["Design", "Frontend", "React"])

      const predefinedTags = ["JavaScript", "TypeScript", "CSS", "HTML", "Node.js", "GraphQL"]

      const handleTagRemove = (tagToRemove: string) => {
        setTags(prev => prev.filter(tag => tag !== tagToRemove))
      }

      const handleTagAdd = (tagToAdd: string) => {
        if (!tags.includes(tagToAdd)) {
          setTags(prev => [...prev, tagToAdd])
        }
      }

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
              Current Tags:
            </Typography>
            <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
              {tags.map(tag => (
                <Chip
                  key={tag}
                  label={tag}
                  onClose={() => handleTagRemove(tag)}
                />
              ))}
              {tags.length === 0 && (
                <Typography level="bodyMedium" style={{ color: "#666" }}>
                  No tags added
                </Typography>
              )}
            </div>
          </div>

          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
              Add Tags:
            </Typography>
            <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
              {predefinedTags
                .filter(tag => !tags.includes(tag))
                .map(tag => (
                  <Chip
                    key={tag}
                    label={tag}
                    variant="outline"
                    onClick={() => handleTagAdd(tag)}
                  />
                ))}
            </div>
          </div>
        </div>
      )
    }
    return <TagDemo />
  },
}

/** Status and category chips */
export const StatusAndCategories: Story = {
  parameters: {
    docs: {
      description: {
        story: "Examples of chips used for status indicators and category labels.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
      <div>
        <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
          Status Indicators:
        </Typography>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <Chip label="Active" color="primary" />
          <Chip label="Error" color="negative" />
          <Chip label="Pending" variant="outline" />
          <Chip label="Completed" onCheck={() => {}} />
          <Chip label="Cancelled" color="negative" variant="outline" />
        </div>
      </div>

      <div>
        <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
          Categories:
        </Typography>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <Chip label="Technology" />
          <Chip label="Design" />
          <Chip label="Business" />
          <Chip label="Marketing" variant="outline" />
          <Chip label="Development" variant="outline" />
        </div>
      </div>

      <div>
        <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
          User Roles:
        </Typography>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <Chip label="Admin" color="negative" />
          <Chip label="Editor" />
          <Chip label="Viewer" variant="outline" />
          <Chip label="Guest" variant="outline" />
        </div>
      </div>
    </div>
  ),
}

/** User search with chips */
export const UserSearch: Story = {
  parameters: {
    docs: {
      description: {
        story: "Example of chips used in a user search interface for selecting team members or users, demonstrating removable selected users and searchable suggestions.",
      },
    },
  },
  render: () => {
    function UserSearchDemo() {
      const [selectedUsers, setSelectedUsers] = useState<string[]>([
        "esther.schanler",
        "rotem.dekel"
      ])

      const [searchQuery, setSearchQuery] = useState("")

      const allUsers = [
        {
          id: "esther.schanler",
          name: "Esther Schanler",
          role: "UX/UI Designer"
        },
        {
          id: "rotem.dekel",
          name: "Rotem Dekel",
          role: "Product Manager"
        },
        {
          id: "may.kishon",
          name: "May Kishon",
          role: "UX/UI Product Designer"
        },
        {
          id: "liron.cohen",
          name: "Liron Cohen",
          role: "Customer Experience"
        },
        {
          id: "amanda.lawrence",
          name: "Amanda Lawrence",
          role: "Customer Experience Designer"
        },
        {
          id: "dor.yehuda",
          name: "Dor Yehuda",
          role: "Customer Experience Designer"
        }
      ]

      const handleUserRemove = (userId: string) => {
        setSelectedUsers(prev => prev.filter(id => id !== userId))
      }

      const handleUserAdd = (userId: string) => {
        if (!selectedUsers.includes(userId)) {
          setSelectedUsers(prev => [...prev, userId])
        }
      }

      const filteredUsers = allUsers.filter(user =>
        !selectedUsers.includes(user.id) &&
        (searchQuery === "" ||
         user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
         user.role.toLowerCase().includes(searchQuery.toLowerCase()))
      )

      return (
        <div style={{
          maxWidth: 350,
          padding: 24,
          borderRadius: 8,
          border: "1px solid #e9ecef"
        }}>
          {/* Search Input */}
          <div style={{
            position: "relative",
            marginBottom: 16,
            display: "flex",
            alignItems: "center",
            backgroundColor: "white",
            border: "1px solid #ddd",
            borderRadius: 8,
            padding: "12px 16px"
          }}>
            <Search size={20} style={{ color: "#666", marginRight: 12 }} />
            <input
              type="text"
              placeholder="Search names, positions, or a team"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{
                border: "none",
                outline: "none",
                flex: 1,
                fontSize: 16,
                color: "#333"
              }}
            />
          </div>

          {/* Selected Users */}
          {selectedUsers.length > 0 && (
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                {selectedUsers.map(userId => {
                  const user = allUsers.find(u => u.id === userId)
                  return user ? (
                    <Chip
                      key={userId}
                      label={user.name}
                      onClose={() => handleUserRemove(userId)}
                      variant="filled"
                      size="medium"
                    />
                  ) : null
                })}
              </div>
            </div>
          )}

          {/* Suggested People */}
          {filteredUsers.length > 0 && (
            <div>
              <Typography level="bodyLarge" style={{
                marginBottom: 12,
                color: "#333",
                fontWeight: 600
              }}>
                {searchQuery ? "Search Results" : "Suggested people"}
              </Typography>
              <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
                {filteredUsers.map(user => (
                  <div
                    key={user.id}
                    onClick={() => handleUserAdd(user.id)}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: 12,
                      padding: "8px 12px",
                      backgroundColor: "white",
                      borderRadius: 6,
                      cursor: "pointer",
                      border: "1px solid transparent",
                      transition: "all 0.2s ease"
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = "#f8f9fa"
                      e.currentTarget.style.borderColor = "#ddd"
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = "white"
                      e.currentTarget.style.borderColor = "transparent"
                    }}
                  >
                    <div
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: "50%",
                        backgroundColor: user.id === "liron.cohen" ? "#9C27B0" : "#666",
                        color: "white",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: 12,
                        fontWeight: "bold"
                      }}
                    >
                      {user.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div>
                      <div style={{ fontWeight: 500, color: "#333" }}>
                        {user.name}
                      </div>
                      <div style={{ fontSize: 12, color: "#666" }}>
                        ({user.role})
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {searchQuery && filteredUsers.length === 0 && (
            <div style={{
              textAlign: "center",
              color: "#666",
              padding: 20,
              fontStyle: "italic"
            }}>
              No users found matching "{searchQuery}"
            </div>
          )}
        </div>
      )
    }
    return <UserSearchDemo />
  },
}
