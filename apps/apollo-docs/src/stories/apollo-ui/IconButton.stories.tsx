import React, { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { IconButton, Typography } from "@apollo/ui"
import {
  ArrowRight,
  Close,
  Download,
  Edit,
  Heart,
  Home,
  InfoCircle,
  Menu,
  Search,
  Setting,
  Star,
  DeleteOutlined,
} from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * IconButton component
 *
 * The IconButton component provides a compact, icon-only button with multiple variants,
 * sizes, colors, and support for link functionality. Perfect for toolbars, navigation,
 * and actions where space is limited.
 *
 * Notes:
 * - Default variant is "icon";
 * - Default size is "large";
 * - Default color is "primary";
 * - Available variants: "filled" | "outline" | "icon";
 * - Available sizes: "large" | "small";
 * - Available colors: "primary" | "negative".
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/IconButton",
  component: IconButton,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2185-22908&m=dev",
    },
    docs: {
      description: {
        component:
          "The IconButton component renders a compact button containing only an icon, no visible text. Supports multiple variants, sizes, colors, and can be rendered as a link when href is provided.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle>Icon button design is included in the button component in 💙 Figma design system.</Subtitle>
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { IconButton } from "@apollo/ui"`} language="tsx" />
          <h2 id="iconbutton-props">Props</h2>
          <ArgTypes />
          <h2 id="iconbutton-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use icon button for actions that are clear from the icon alone, frequent actions like Close or Clear.",
              "Make sure the icon meaning is universally recognizable (for example, the trash icon for delete).",
              "Use button tooltips to give extra context when necessary.",
            ]}
          />
          <h2 id="iconbutton-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide an <code>aria-label</code> for screen readers to
                understand the button's purpose.
              </>,
              <>
                Use the <code>disabled</code> prop to disable buttons that are
                not currently actionable, ensuring they are not focusable.
              </>,
              <>
                For link buttons using <code>href</code>, ensure the
                destination is clear from context or provide additional
                information.
              </>,
              <>
                Choose universally recognizable icons for actions (e.g., trash for delete) to ensure users easily understand each Button Icon’s purpose.
              </>,
              <>
                When possible, provide tooltips that describe the action of the Button Icon. This enhances usability for users who may not immediately recognize an icon’s purpose.
              </>,
            ]}
          />
          <h2 id="iconbutton-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The IconButton component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloIconButton-root",
                description: "Styles applied to the icon button root element",
                usageNotes: "Use for overall icon button styling and positioning",
              },
            ]}
          />
          <h2 id="iconbutton-examples">Examples</h2>
          <Stories title="" />
          <h2 id="iconbutton-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <IconButton aria-label="Edit item">
                        <Edit />
                      </IconButton>
                      <IconButton aria-label="Delete item" color="negative">
                        <DeleteOutlined />
                      </IconButton>
                    </div>
                  ),
                  description: "Use clear, recognizable icons with descriptive aria-labels",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <IconButton>
                        <Edit />
                      </IconButton>
                      <IconButton>
                        <DeleteOutlined />
                      </IconButton>
                    </div>
                  ),
                  description:
                    "Don't omit accessibility labels or use unclear icons",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <IconButton aria-label="Save changes">
                        <Download />
                      </IconButton>
                      <IconButton aria-label="Cancel" variant="outline">
                        <Close />
                      </IconButton>
                      <IconButton aria-label="More options" variant="icon">
                        <Menu />
                      </IconButton>
                    </div>
                  ),
                  description:
                    "Use appropriate hierarchy - filled for primary, outline for secondary, text for tertiary actions",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <IconButton aria-label="Save changes">
                        <Download />
                      </IconButton>
                      <IconButton aria-label="Cancel">
                        <Close />
                      </IconButton>
                      <IconButton aria-label="More options">
                        <Menu />
                      </IconButton>
                    </div>
                  ),
                  description:
                    "Don't use the same variant for all actions - it creates confusion about priority",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    variant: {
      control: { type: "radio" },
      options: ["filled", "outline", "icon"],
      description: "Visual style variant of the button. Default is 'icon'.",
      table: {
        type: { summary: '"filled" | "outline" | "icon"' },
        defaultValue: { summary: "icon" },
      },
    },
    size: {
      control: { type: "radio" },
      options: ["large", "small"],
      description: "Visual size of the button. Default is 'large'.",
      table: {
        type: { summary: '"large" | "small"' },
        defaultValue: { summary: "large" },
      },
    },
    color: {
      control: { type: "radio" },
      options: ["primary", "negative"],
      description: "Color theme of the button. Default is 'primary'.",
      table: {
        type: { summary: '"primary" | "negative"' },
        defaultValue: { summary: "primary" },
      },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disables the button.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    href: {
      control: { type: "text" },
      description:
        "When provided, renders the button as an anchor element with this URL.",
      table: { type: { summary: "string" } },
    },
    onClick: {
      control: false,
      description: "Callback fired when the button is clicked.",
      table: {
        type: {
          summary: "(event: React.MouseEvent<HTMLButtonElement>) => void",
        },
      },
    },
    children: {
      control: false,
      description: "The icon content of the button (typically an SVG icon).",
      table: { type: { summary: "ReactNode" } },
    },
    ref: {
      control: false,
      description: "Ref for the underlying button element.",
      table: { type: { summary: "React.Ref<HTMLButtonElement>" } },
    },
  },
  args: {
    children: <Heart />,
    variant: "icon",
    size: "large",
    color: "primary",
    disabled: false,
  },
} satisfies Meta<typeof IconButton>

export default meta

type Story = StoryObj<typeof IconButton>

/** Default IconButton (demonstrates default text variant) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview IconButton with default settings. The component defaults to variant 'text', size 'large', and color 'primary'.",
      },
    },
  },
  args: {
    children: <Heart />,
    "aria-label": "Favorite",
  },
}

/** IconButton with different variants (filled, outline, text) */
export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases all available IconButton variants: filled, outline, and text (default).",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <IconButton {...args} variant="filled" aria-label="Filled variant">
        <Heart />
      </IconButton>
      <IconButton {...args} variant="outline" aria-label="Outline variant">
        <Heart />
      </IconButton>
      <IconButton {...args} variant="icon" aria-label="Icon variant">
        <Heart />
      </IconButton>
    </div>
  ),
}

/** IconButton with different sizes (large, small) */
export const Sizes: Story = {
  parameters: {
    docs: {
      description: {
        story: "Showcases both available sizes: large (default) and small.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <IconButton {...args} size="large" aria-label="Large size">
        <Heart />
      </IconButton>
      <IconButton {...args} size="small" aria-label="Small size">
        <Heart />
      </IconButton>
    </div>
  ),
}

/** IconButton with different colors (primary, negative) */
export const Colors: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases both available color themes: primary (default) and negative.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <IconButton {...args} color="primary" aria-label="Primary color">
        <Heart />
      </IconButton>
      <IconButton {...args} color="negative" aria-label="Negative color">
        <Heart />
      </IconButton>
    </div>
  ),
}

/** IconButton with different icons */
export const Icons: Story = {
  parameters: {
    docs: {
      description: {
        story: "IconButtons with various common icons for different use cases.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <IconButton {...args} aria-label="Search">
        <Search />
      </IconButton>
      <IconButton {...args} aria-label="Edit">
        <Edit />
      </IconButton>
      <IconButton {...args} aria-label="Download">
        <Download />
      </IconButton>
      <IconButton {...args} aria-label="Settings">
        <Setting />
      </IconButton>
      <IconButton {...args} aria-label="Home">
        <Home />
      </IconButton>
      <IconButton {...args} aria-label="Close">
        <Close />
      </IconButton>
      <IconButton {...args} aria-label="Menu">
        <Menu />
      </IconButton>
      <IconButton {...args} aria-label="Star">
        <Star />
      </IconButton>
    </div>
  ),
}

/** IconButton disabled states */
export const Disabled: Story = {
  parameters: {
    docs: {
      description: {
        story: "IconButtons in disabled state across all variants and colors.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <IconButton disabled aria-label="Disabled filled">
        <Heart />
      </IconButton>
      <IconButton disabled variant="outline" aria-label="Disabled outline">
        <Heart />
      </IconButton>
      <IconButton disabled variant="icon" aria-label="Disabled text">
        <Heart />
      </IconButton>
      <IconButton disabled color="negative" aria-label="Disabled negative">
        <Heart />
      </IconButton>
      <IconButton disabled size="small" aria-label="Disabled small">
        <Heart />
      </IconButton>
    </div>
  ),
}

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of IconButton states including default, hover, focus, active, and disabled states across different variants.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 20,
          alignItems: "center",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Filled IconButtons</Typography>
          <div style={{ display: "flex", gap: 8 }}>
            <IconButton variant="filled" aria-label="Default filled">
              <Heart />
            </IconButton>
            <IconButton variant="filled" disabled aria-label="Disabled filled">
              <Heart />
            </IconButton>
          </div>
          <div style={{ display: "flex", gap: 8 }}>
            <IconButton variant="filled" color="negative" aria-label="Negative filled">
              <DeleteOutlined />
            </IconButton>
            <IconButton variant="filled" color="negative" disabled aria-label="Disabled negative filled">
              <DeleteOutlined />
            </IconButton>
          </div>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Outline IconButtons</Typography>
          <div style={{ display: "flex", gap: 8 }}>
            <IconButton variant="outline" aria-label="Default outline">
              <Edit />
            </IconButton>
            <IconButton variant="outline" disabled aria-label="Disabled outline">
              <Edit />
            </IconButton>
          </div>
          <div style={{ display: "flex", gap: 8 }}>
            <IconButton variant="outline" color="negative" aria-label="Negative outline">
              <Close />
            </IconButton>
            <IconButton variant="outline" color="negative" disabled aria-label="Disabled negative outline">
              <Close />
            </IconButton>
          </div>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Icon IconButtons</Typography>
          <div style={{ display: "flex", gap: 8 }}>
            <IconButton variant="icon" aria-label="Default text">
              <Setting />
            </IconButton>
            <IconButton variant="icon" disabled aria-label="Disabled text">
              <Setting />
            </IconButton>
          </div>
          <div style={{ display: "flex", gap: 8 }}>
            <IconButton variant="icon" color="negative" aria-label="Negative text">
              <DeleteOutlined />
            </IconButton>
            <IconButton variant="icon" color="negative" disabled aria-label="Disabled negative text">
              <DeleteOutlined />
            </IconButton>
          </div>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Different Sizes</Typography>
          <div style={{ display: "flex", gap: 8, alignItems: "center" }}>
            <IconButton size="large" aria-label="Large size">
              <Star />
            </IconButton>
            <IconButton size="small" aria-label="Small size">
              <Star />
            </IconButton>
          </div>
          <div style={{ display: "flex", gap: 8, alignItems: "center" }}>
            <IconButton size="large" variant="outline" aria-label="Large outline">
              <Download />
            </IconButton>
            <IconButton size="small" variant="outline" aria-label="Small outline">
              <Download />
            </IconButton>
          </div>
        </div>
      </div>
    )
  },
}

/** IconButton as links using href */
export const Links: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "IconButtons rendered as anchor elements using the href prop, perfect for navigation actions.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <IconButton href="https://example.com" variant="icon" aria-label="External link">
        <ArrowRight />
      </IconButton>
      <IconButton href="https://example.com" variant="outline" target="_blank" aria-label="Open in new tab">
        <InfoCircle />
      </IconButton>
      <IconButton href="#home" variant="filled" aria-label="Go to home">
        <Home />
      </IconButton>
      <IconButton href="https://example.com" variant="icon" color="negative" aria-label="Negative link">
        <Close />
      </IconButton>
    </div>
  ),
}

/** Toolbar example with IconButtons */
export const Toolbar: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Real-world example of IconButtons used in a toolbar layout with different actions and separators.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        gap: 8,
        padding: "8px 12px",
        backgroundColor: "#f8f9fa",
        borderRadius: 8,
        border: "1px solid #e9ecef",
      }}
    >
      <IconButton size="small" variant="icon" aria-label="Menu">
        <Menu />
      </IconButton>
      <div
        style={{ width: "1px", height: "24px", backgroundColor: "#e0e0e0" }}
      />
      <IconButton size="small" variant="icon" aria-label="Go back">
        <ArrowRight style={{ transform: "rotate(180deg)" }} />
      </IconButton>
      <IconButton size="small" variant="icon" aria-label="Go forward">
        <ArrowRight />
      </IconButton>
      <div
        style={{ width: "1px", height: "24px", backgroundColor: "#e0e0e0" }}
      />
      <IconButton size="small" variant="icon" aria-label="Edit">
        <Edit />
      </IconButton>
      <IconButton size="small" variant="icon" color="negative" aria-label="Delete">
        <DeleteOutlined />
      </IconButton>
      <div style={{ flex: 1 }} />
      <IconButton size="small" variant="icon" aria-label="Search">
        <Search />
      </IconButton>
      <IconButton size="small" variant="icon" aria-label="Settings">
        <Setting />
      </IconButton>
    </div>
  ),
}

/** Interactive playground with controls */
export const InteractivePlayground: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Interactive playground to experiment with different IconButton configurations and see real-time changes.",
      },
    },
  },
  render: () => {
    function PlaygroundDemo() {
      const [config, setConfig] = useState({
        size: "large" as "small" | "large",
        color: "primary" as "primary" | "negative",
        variant: "icon" as "filled" | "outline" | "icon",
        disabled: false,
        icon: "heart" as
          | "heart"
          | "star"
          | "edit"
          | "download"
          | "search"
          | "settings"
          | "home"
          | "close",
      })

      const iconMap = {
        heart: <Heart />,
        star: <Star />,
        edit: <Edit />,
        download: <Download />,
        search: <Search />,
        settings: <Setting />,
        home: <Home />,
        close: <Close />,
      }

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              padding: 40,
              backgroundColor: "#f8f9fa",
              borderRadius: 8,
              border: "2px dashed #dee2e6",
            }}
          >
            <IconButton
              size={config.size}
              color={config.color}
              variant={config.variant}
              disabled={config.disabled}
              aria-label={`${config.icon} button`}
            >
              {iconMap[config.icon]}
            </IconButton>
          </div>

          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
              gap: 16,
              padding: 16,
              backgroundColor: "#ffffff",
              borderRadius: 8,
              border: "1px solid #e9ecef",
            }}
          >
            <div>
              <label htmlFor="size-select" style={{ display: "block", marginBottom: 8, fontWeight: 500 }}>
                Size
              </label>
              <select
                id="size-select"
                value={config.size}
                onChange={(e) =>
                  setConfig({ ...config, size: e.target.value as "small" | "large" })
                }
                style={{ width: "100%", padding: 8, borderRadius: 4, border: "1px solid #ccc" }}
              >
                <option value="small">Small</option>
                <option value="large">Large</option>
              </select>
            </div>

            <div>
              <label htmlFor="color-select" style={{ display: "block", marginBottom: 8, fontWeight: 500 }}>
                Color
              </label>
              <select
                id="color-select"
                value={config.color}
                onChange={(e) =>
                  setConfig({ ...config, color: e.target.value as "primary" | "negative" })
                }
                style={{ width: "100%", padding: 8, borderRadius: 4, border: "1px solid #ccc" }}
              >
                <option value="primary">Primary</option>
                <option value="negative">Negative</option>
              </select>
            </div>

            <div>
              <label htmlFor="variant-select" style={{ display: "block", marginBottom: 8, fontWeight: 500 }}>
                Variant
              </label>
              <select
                id="variant-select"
                value={config.variant}
                onChange={(e) =>
                  setConfig({
                    ...config,
                    variant: e.target.value as "filled" | "outline" | "icon",
                  })
                }
                style={{ width: "100%", padding: 8, borderRadius: 4, border: "1px solid #ccc" }}
              >
                <option value="filled">Filled</option>
                <option value="outline">Outline</option>
                <option value="icon">Icon</option>
              </select>
            </div>

            <div>
              <label htmlFor="icon-select" style={{ display: "block", marginBottom: 8, fontWeight: 500 }}>
                Icon
              </label>
              <select
                id="icon-select"
                value={config.icon}
                onChange={(e) =>
                  setConfig({
                    ...config,
                    icon: e.target.value as keyof typeof iconMap,
                  })
                }
                style={{ width: "100%", padding: 8, borderRadius: 4, border: "1px solid #ccc" }}
              >
                <option value="heart">Heart</option>
                <option value="star">Star</option>
                <option value="edit">Edit</option>
                <option value="download">Download</option>
                <option value="search">Search</option>
                <option value="settings">Settings</option>
                <option value="home">Home</option>
                <option value="close">Close</option>
              </select>
            </div>

            <div style={{ display: "flex", alignItems: "end" }}>
              <label style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <input
                  type="checkbox"
                  checked={config.disabled}
                  onChange={(e) =>
                    setConfig({ ...config, disabled: e.target.checked })
                  }
                />
                {" "}Disabled
              </label>
            </div>
          </div>

          <div
            style={{
              padding: 16,
              backgroundColor: "#f8f9fa",
              borderRadius: 8,
              fontFamily: "monospace",
              fontSize: 14,
              lineHeight: 1.5,
            }}
          >
            <strong>Generated Code:</strong>
            <br />
            {`<IconButton`}
            <br />
            {`  size="${config.size}"`}
            <br />
            {`  color="${config.color}"`}
            <br />
            {`  variant="${config.variant}"`}
            {config.disabled && (
              <>
                <br />
                {`  disabled`}
              </>
            )}
            <br />
            {`  aria-label="${config.icon} button"`}
            <br />
            {`>`}
            <br />
            {`  <${config.icon.charAt(0).toUpperCase() + config.icon.slice(1)} />`}
            <br />
            {`</IconButton>`}
          </div>
        </div>
      )
    }
    return <PlaygroundDemo />
  },
}

/** Migration guide comparing legacy vs new IconButton implementations */
export const Migration: Story = {
  parameters: {
    docs: {
      description: {
        story: "Visual comparison between legacy IconButton (@design-systems/apollo-ui) and new Apollo IconButton (@apollo/ui) showing the migration path and key differences.",
      },
    },
  },
  render: () => {
    function MigrationDemo() {
      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 32 }}>
          <style>
            {`
              .migration-section {
                padding: 20px;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                background: #f9fafb;
              }
              .migration-header {
                font-weight: 600;
                margin-bottom: 16px;
                color: #374151;
              }
              .migration-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
                margin-top: 16px;
              }
              .migration-column {
                display: flex;
                flex-direction: column;
                gap: 12px;
              }
              .migration-label {
                font-weight: 500;
                font-size: 14px;
                color: #6b7280;
                margin-bottom: 8px;
              }
              .code-block {
                background: #1f2937;
                color: #f3f4f6;
                padding: 12px;
                border-radius: 6px;
                font-family: 'Monaco', 'Menlo', monospace;
                font-size: 12px;
                line-height: 1.4;
                overflow-x: auto;
              }
              .removed { color: #fca5a5; }
              .added { color: #86efac; }
            `}
          </style>

          {/* Import Migration */}
          <div className="migration-section">
            <div className="migration-header">📦 Import Migration</div>
            <div className="migration-grid">
              <div className="migration-column">
                <div className="migration-label">❌ Legacy Package</div>
                <div className="code-block">
                  <div className="removed">{'import { IconButton } from'}</div>
                  <div className="removed">{'  "@design-systems/apollo-ui"'}</div>
                </div>
              </div>
              <div className="migration-column">
                <div className="migration-label">✅ New Package</div>
                <div className="code-block">
                  <div className="added">{'import { IconButton } from'}</div>
                  <div className="added">{'  "@apollo/ui"'}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Size Migration */}
          <div className="migration-section">
            <div className="migration-header">📏 Size Migration</div>
            <div className="migration-grid">
              <div className="migration-column">
                <div className="migration-label">❌ Legacy (3 sizes)</div>
                <div className="code-block">
                  <div>{'<IconButton size="small"><Heart /></IconButton>'}</div>
                  <div className="removed">{'<IconButton size="medium"><Heart /></IconButton>'}</div>
                  <div>{'<IconButton size="large"><Heart /></IconButton>'}</div>
                  <div style={{ marginTop: 8, color: '#9ca3af', fontSize: '11px' }}>
                    Default: "medium"
                  </div>
                </div>
              </div>
              <div className="migration-column">
                <div className="migration-label">✅ New (2 sizes)</div>
                <div className="code-block">
                  <div className="added">{'<IconButton size="small"><Heart /></IconButton>'}</div>
                  <div className="added">{'<IconButton size="large"><Heart /></IconButton>'}</div>
                  <div style={{ marginTop: 8, color: '#9ca3af', fontSize: '11px' }}>
                    Default: "large"
                  </div>
                </div>
                <div style={{ display: "flex", gap: 8, alignItems: "center", marginTop: 8 }}>
                  <IconButton size="small" aria-label="Small heart">
                    <Heart />
                  </IconButton>
                  <IconButton size="large" aria-label="Large heart">
                    <Heart />
                  </IconButton>
                </div>
              </div>
            </div>
          </div>

          {/* Variant System Introduction */}
          <div className="migration-section">
            <div className="migration-header">🎨 Variant System Introduction</div>
            <div className="migration-grid">
              <div className="migration-column">
                <div className="migration-label">❌ Legacy (No variant prop)</div>
                <div className="code-block">
                  <div>{'<IconButton><Heart /></IconButton>'}</div>
                  <div style={{ marginTop: 8, color: '#9ca3af', fontSize: '11px' }}>
                    Always renders as text-style button
                  </div>
                </div>
              </div>
              <div className="migration-column">
                <div className="migration-label">✅ New (Variant options)</div>
                <div className="code-block">
                  <div className="added">{'<IconButton variant="icon"><Heart /></IconButton>'}</div>
                  <div className="added">{'<IconButton variant="filled"><Heart /></IconButton>'}</div>
                  <div className="added">{'<IconButton variant="outline"><Heart /></IconButton>'}</div>
                  <div style={{ marginTop: 8, color: '#9ca3af', fontSize: '11px' }}>
                    Default: "icon" (same visual as legacy)
                  </div>
                </div>
                <div style={{ display: "flex", gap: 8, alignItems: "center", marginTop: 8 }}>
                  <IconButton variant="icon" aria-label="Icon variant">
                    <Heart />
                  </IconButton>
                  <IconButton variant="filled" aria-label="Filled variant">
                    <Heart />
                  </IconButton>
                  <IconButton variant="outline" aria-label="Outline variant">
                    <Heart />
                  </IconButton>
                </div>
              </div>
            </div>
          </div>

          {/* Color Migration */}
          <div className="migration-section">
            <div className="migration-header">🎨 Color Migration</div>
            <div className="migration-grid">
              <div className="migration-column">
                <div className="migration-label">❌ Legacy</div>
                <div className="code-block">
                  <div>{'<IconButton color="primary"><Heart /></IconButton>'}</div>
                  <div className="removed">{'<IconButton color="danger"><Trash /></IconButton>'}</div>
                </div>
              </div>
              <div className="migration-column">
                <div className="migration-label">✅ New</div>
                <div className="code-block">
                  <div>{'<IconButton color="primary"><Heart /></IconButton>'}</div>
                  <div className="added">{'<IconButton color="negative"><DeleteOutlined /></IconButton>'}</div>
                </div>
                <div style={{ display: "flex", gap: 8, marginTop: 8 }}>
                  <IconButton color="primary" aria-label="Primary color">
                    <Heart />
                  </IconButton>
                  <IconButton color="negative" aria-label="Negative color">
                    <DeleteOutlined />
                  </IconButton>
                </div>
              </div>
            </div>
          </div>

          {/* Default Behavior Changes */}
          <div className="migration-section">
            <div className="migration-header">⚙️ Default Behavior Changes</div>
            <div className="migration-grid">
              <div className="migration-column">
                <div className="migration-label">❌ Legacy Defaults</div>
                <div className="code-block">
                  <div>{'<IconButton><Heart /></IconButton>'}</div>
                  <div style={{ marginTop: 8, color: '#9ca3af', fontSize: '11px' }}>
                    • size="medium" (32px)<br />
                    • Always text-style appearance
                  </div>
                </div>
              </div>
              <div className="migration-column">
                <div className="migration-label">✅ New Defaults</div>
                <div className="code-block">
                  <div className="added">{'<IconButton><Heart /></IconButton>'}</div>
                  <div style={{ marginTop: 8, color: '#9ca3af', fontSize: '11px' }}>
                    • size="large" (40px)<br />
                    • variant="icon" (text-style appearance)
                  </div>
                </div>
                <div style={{ marginTop: 8 }}>
                  <IconButton aria-label="Default IconButton">
                    <Heart />
                  </IconButton>
                </div>
              </div>
            </div>
          </div>

          {/* Summary */}
          <div className="migration-section">
            <div className="migration-header">📋 Migration Checklist</div>
            <div style={{ fontSize: 14, lineHeight: 1.6 }}>
              <div>✅ Update import: <code>@design-systems/apollo-ui</code> → <code>@apollo/ui</code></div>
              <div>✅ Replace medium size: <code>size="medium"</code> → <code>size="small"</code> or <code>size="large"</code></div>
              <div>✅ Update color: <code>color="danger"</code> → <code>color="negative"</code></div>
              <div>✅ Consider new variants: Add <code>variant="filled"</code> or <code>variant="outline"</code> for enhanced styling</div>
              <div>✅ Review default size: New default is <code>"large"</code> instead of <code>"medium"</code></div>
              <div>✅ Test visual appearance: Ensure icons look correct with new sizing</div>
            </div>
          </div>
        </div>
      )
    }
    return <MigrationDemo />
  },
}
