import React, { useMemo, useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { Autocomplete, Button, IconButton, Modal, Typography } from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

// Shared example data
const baseOptions = [
  { label: "Electronics — Laptops & Ultrabooks", value: "option1" },
  { label: "Electronics — Smartphones", value: "option2" },
  { label: "Home & Kitchen — Cookware Sets", value: "option3" },
  { label: "Home & Kitchen — Small Appliances", value: "option4" },
  { label: "Clothing — Men's Jackets", value: "option5" },
  { label: "Clothing — Women's Dresses", value: "option6" },
  { label: "Beauty & Personal Care", value: "option7" },
  { label: "Sports & Outdoors — Fitness Equipment", value: "option8" },
]

const meta = {
  title: "@apollo∕ui/Components/Inputs/Autocomplete",
  component: Autocomplete,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2188-22911&m=dev",
    },
    docs: {
      description: {
        component:
          "Autocomplete lets users quickly find and select from a list of options. It supports single and multiple selection, async search, infinite loading, disabled and error states, and rich configuration for labels and helper content. It allows merchants to quickly search through and select from large collections of options.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Autocomplete } from "@apollo/ui"`}
            language="tsx"
          />

          <h2 id="autocomplete-props">Props</h2>
          <ArgTypes />

          <h2 id="autocomplete-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Be clearly labeled so it’s obvious to the user what type of options will be available",
              "Indicate a loading state to the user while option data is being populated",
              "Prefer async onSearch for large datasets; use filterLogic for client-side filtering.",
              "Use showSelectAll only when it benefits users (e.g., long multi-select lists).",
              "Provide a clear empty state via noOptionsComponent when nothing matches.",
            ]}
          />

          <h2 id="autocomplete-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide a <code>label</code> for screen readers and
                clarity.
              </>,
              <>
                Do not use <code>placeholder</code> as the only means to convey
                purpose.
              </>,
              <>
                Ensure keyboard navigation works: Arrow keys to move, Enter to
                select, Esc to close.
              </>,
              <>
                Use <code>helperText</code> with <code>error</code> to
                communicate validation issues.
              </>,
              <>
                Leverage <code>aria-</code> props (<code>aria-label</code>,{" "}
                <code>aria-labelledby</code>, <code>aria-describedby</code>)
                when needed.
              </>,
            ]}
          />

          <h2 id="autocomplete-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Autocomplete component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloAutocomplete-root",
                description:
                  "Styles applied to the autocomplete root container",
                usageNotes:
                  "Use for overall autocomplete styling and positioning",
              },
              {
                cssClassName: ".ApolloAutocomplete-inputRoot",
                description: "Styles applied to the input element",
                usageNotes:
                  "Contains input field styling including focus states",
              },
              {
                cssClassName: ".ApolloAutocomplete-inputWrapper",
                description:
                  "Styles applied to the wrapper around input and chips",
                usageNotes:
                  "Contains input and chip styling including overflow handling",
              },
              {
                cssClassName: ".ApolloAutocomplete-trigger",
                description: "Styles applied to the trigger element",
                usageNotes: "The clickable element that opens the dropdown",
              },
              {
                cssClassName: ".ApolloAutocomplete-dropdownRoot",
                description: "Styles applied to the dropdown menu root",
                usageNotes:
                  "Contains dropdown menu styling including border and background",
              },
              {
                cssClassName: ".ApolloAutocomplete-menuItem",
                description: "Styles applied to each menu item",
                usageNotes: "Base styling for dropdown options",
              },
              {
                cssClassName: ".ApolloAutocomplete-checkboxItem",
                description:
                  "Styles applied to the checkbox inside a menu item",
                usageNotes: "Styling for checkboxes in multi-select mode",
              },
              {
                cssClassName: ".ApolloAutocomplete-chipContainer",
                description:
                  "Styles applied to the container of selected chips",
                usageNotes:
                  "Contains chip group styling including overflow handling",
              },
              {
                cssClassName: ".ApolloAutocomplete-chip",
                description: "Styles applied to each selected value chip",
                usageNotes: "Contains chip styling including close button",
              },
              {
                cssClassName: ".ApolloAutocomplete-clearButton",
                description: "Styles applied to the clear selection button",
                usageNotes: "Button to clear all selected values",
              },
              {
                cssClassName: ".ApolloAutocomplete-toggleButton",
                description: "Styles applied to the dropdown toggle button",
                usageNotes: "Button to open/close the dropdown menu",
              },
              {
                cssClassName: ".ApolloAutocomplete-chevronIcon",
                description: "Styles applied to the chevron icon",
                usageNotes: "Icon within the dropdown toggle button",
              },
              {
                cssClassName: ".ApolloAutocomplete-scrollTrigger",
                description:
                  "Styles applied to the infinite scroll trigger element",
                usageNotes: "Element to trigger loading more options",
              },
            ]}
          />

          <h2 id="autocomplete-examples">Examples</h2>
          <Stories title="" />

          <h2 id="autocomplete-dos-donts">Do’s and Don’ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ minWidth: 300 }}>
                      <Autocomplete
                        label="Fruits"
                        placeholder="Type to search…"
                        fullWidth
                        options={[
                          { label: "Apple", value: "apple" },
                          { label: "Banana", value: "banana" },
                        ]}
                      />
                    </div>
                  ),
                  description:
                    "Provide a clear label and concise placeholder to guide users.",
                },
                negative: {
                  component: (
                    <div style={{ minWidth: 300 }}>
                      <Autocomplete
                        label="Fruits"
                        placeholder="Search for all available product names in the catalog"
                        fullWidth
                        options={[
                          { label: "Apple", value: "apple" },
                          { label: "Banana", value: "banana" },
                        ]}
                      />
                    </div>
                  ),
                  description:
                    "Avoid long instructional placeholders; use helperText instead.",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ minWidth: 300 }}>
                      <Autocomplete
                        multiple
                        options={[
                          { label: "Apple", value: "apple" },
                          { label: "Banana", value: "banana" },
                          { label: "Cherry", value: "cherry" },
                          { label: "Dragonfruit", value: "dragonfruit" },
                          { label: "Elderberry", value: "elderberry" },
                          { label: "Fig", value: "fig" },
                          { label: "Grape", value: "grape" },
                          { label: "Honeydew", value: "honeydew" },
                          { label: "Kiwi", value: "kiwi" },
                          { label: "Lemon", value: "lemon" },
                        ]}
                        showSelectAll
                        label="Fruits"
                        fullWidth
                        placeholder="Type to search..."
                      />
                    </div>
                  ),
                  description:
                    "Use Select All for longer multi-select lists to speed selection.",
                },
                negative: {
                  component: (
                    <div style={{ minWidth: 300 }}>
                      <Autocomplete
                        multiple
                        options={[
                          { label: "Apple", value: "apple" },
                          { label: "Banana", value: "banana" },
                        ]}
                        showSelectAll
                        label="Fruits"
                        fullWidth
                        placeholder="Type to search..."
                      />
                    </div>
                  ),
                  description:
                    "Avoid Select All for very small lists where it adds noise.",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    // Core behavior
    multiple: {
      control: { type: "boolean" },
      description:
        "Enable multiple selection mode (renders chips and select all).",
      table: { type: { summary: "boolean" } },
    },
    options: {
      control: "object",
      description:
        "Array of options { label: string; value: any; disabled?: boolean }.",
      table: {
        type: {
          summary: "Array<{ label: string; value: any; disabled?: boolean }>",
        },
      },
    },
    value: {
      control: false,
      description: "Selected value(s). Type depends on multiple prop",
      table: { type: { summary: "any | any[]" } },
    },
    defaultValue: {
      control: false,
      description: "Default value(s). Type depends on multiple prop.",
      table: { type: { summary: "any | any[]" } },
    },
    onChange: {
      control: false,
      description:
        "Callback fired when value changes. For single: (value?: any, event?: React.MouseEvent<HTMLElement>) => void. For multiple: (value?: any[], event?: React.MouseEvent<HTMLElement>) => void.",
      table: {
        type: {
          summary:
            "(value: any | any[], event?: React.MouseEvent<HTMLElement>) => void",
        },
      },
    },

    // UX and search
    search: {
      control: "text",
      description: "Controlled search value.",
      table: { type: { summary: "string" } },
    },
    disableSearch: {
      control: "boolean",
      description: "Hide the search input inside the menu.",
      table: { type: { summary: "boolean" } },
    },
    debounceMs: {
      control: "number",
      description: "Debounce in ms for onSearch callback.",
      table: { type: { summary: "number" } },
    },
    onSearch: {
      control: false,
      description:
        "Async search callback; return a promise to coordinate loading state.",
      table: {
        type: { summary: "(search: string) => Promise<void> | void" },
      },
    },
    filterLogic: {
      control: false,
      description: "Custom client-side filter function when searching.",
      table: {
        type: { summary: "(options: Option[], search: string) => Option[]" },
      },
    },
    noOptionsComponent: {
      control: false,
      description: "Custom component to render when no options are available.",
      table: { type: { summary: "ReactNode" } },
    },

    // Infinite loading
    onLoadMore: {
      control: false,
      description: "Callback to load more options when user requests more.",
      table: { type: { summary: "() => Promise<void> | void" } },
    },
    loadingMore: {
      control: "boolean",
      description: "Indicates more results are being loaded.",
      table: { type: { summary: "boolean" } },
    },
    hasMore: {
      control: "boolean",
      description: "Whether there are more options to load.",
      table: { type: { summary: "boolean" } },
    },
    loadMoreLabel: {
      control: "text",
      description:
        "Text or ReactNode label for the load-more item at the end of the list.",
      table: { type: { summary: "string | ReactNode" } },
    },
    loadingComponent: {
      control: false,
      description: "Custom component to render when loading.",
      table: { type: { summary: "ReactNode" } },
    },
    // Visuals and field state
    size: {
      control: { type: "radio" },
      options: ["small", "medium"],
      description: "Component size.",
      table: { type: { summary: '"small" | "medium"' } },
    },
    fullWidth: {
      control: "boolean",
      description: "Stretch to container width.",
      table: { type: { summary: "boolean" } },
    },
    error: {
      control: "boolean",
      description: "Error state (affects styles).",
      table: { type: { summary: "boolean" } },
    },
    required: {
      control: "boolean",
      description: "Marks field as required.",
      table: { type: { summary: "boolean" } },
    },
    disabled: {
      control: "boolean",
      description: "Disable the field.",
      table: { type: { summary: "boolean" } },
    },

    // Multiple mode helpers
    showSelectAll: {
      control: "boolean",
      description: "Show Select All option (multiple mode).",
      table: { type: { summary: "boolean" } },
    },
    selectAllText: {
      control: "text",
      description: "Custom text for Select All (multiple mode).",
      table: { type: { summary: "string" } },
    },
    hideCheckbox: {
      control: "boolean",
      description: "Hide the checkbox UI in multiple mode.",
      table: { type: { summary: "boolean" } },
    },

    // Labels and helpers
    placeholder: {
      control: "text",
      description: "Input placeholder.",
      table: { defaultValue: { summary: "" }, type: { summary: "string" } },
    },
    label: {
      control: "text",
      description: "Field label (rendered by internal Field).",
      table: { type: { summary: "string" } },
    },
    helperText: {
      control: "text",
      description: "Helper or error text below the field.",
      table: { type: { summary: "string" } },
    },
    labelDecorator: {
      control: false,
      description: "ReactNode rendered next to label.",
      table: { type: { summary: "ReactNode" } },
    },
    helperTextDecorator: {
      control: false,
      description: "ReactNode rendered next to helper text.",
      table: { type: { summary: "ReactNode" } },
    },
    menuLabelText: {
      control: "text",
      description: "Label at the top of the menu list.",
      table: { type: { summary: "string" } },
    },
    onFocus: {
      control: false,
      description: "Callback fired when input is focused.",
      table: {
        type: {
          summary: "(event: React.FocusEvent<HTMLInputElement>) => void",
        },
      },
    },
    onBlur: {
      control: false,
      description: "Callback fired when input loses focus.",
      table: {
        type: {
          summary: "(event: React.FocusEvent<HTMLInputElement>) => void",
        },
      },
    },
    className: { control: false, description: "Additional CSS classes.", table: { type: { summary: "string" } } },
  },
  args: {
    options: baseOptions,
    placeholder: "Select option…",
    fullWidth: false,
    disabled: false,
    error: false,
    required: false,
    size: "medium",
    disableSearch: false,
    showSelectAll: false,
    hideCheckbox: false,
    debounceMs: 200,
    menuLabelText: undefined,
  },
} satisfies Meta<typeof Autocomplete>

export default meta

type Story = StoryObj<typeof Autocomplete>

// Basic single-select (uncontrolled, interactive)
export const Overview: Story = {
  render: (args) => {
    return <Autocomplete {...args} />
  },
  args: {
    label: "Label",
    helperText: "Helper text",
  },
}

export const Sizes: Story = {
  render: (args) => (
    <div style={{ display: "flex", gap: 16 }}>
      <div style={{ minWidth: 260 }}>
        <Autocomplete
          {...args}
          label="Small"
          size="small"
          placeholder="Small size"
        />
      </div>
      <div style={{ minWidth: 260 }}>
        <Autocomplete
          {...args}
          label="Medium"
          size="medium"
          placeholder="Medium size"
        />
      </div>
    </div>
  ),
}

export const FullWidth: Story = {
  parameters: { layout: "padded" },
  render: (args) => <Autocomplete {...args} fullWidth />,
  args: {
    label: "Label",
    helperText: "Helper text",
  },
}

/** Select with label */
export const Label: Story = {
  parameters: {
    docs: {
      description: {
        story: "Autocomplete with label and label decorator.",
      },
    },
  },
  render: (args) => {
    const [open, setOpen] = useState(false)
    const close = () => setOpen(false)
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
          gap: 20,
          alignItems: "center",
        }}
      >
        <Typography level="bodyLarge">Default</Typography>
        <Autocomplete
          {...args}
          label="Category"
          placeholder="Select a category"
          fullWidth
        />
        <Typography level="bodyLarge">Label Decorator (Clickable)</Typography>
        <Autocomplete
          {...args}
          label="Category"
          placeholder="Select a category"
          labelDecorator={
            <IconButton
              type="button"
              onClick={() => setOpen(true)}
              style={{
                padding: 0,
                background: "none",
                height: "fit-content",
                minHeight: "fit-content",
                width: "fit-content",
                minWidth: "fit-content",
              }}
              aria-label="More info"
              size="small"
            >
              <InfoCircle size={12} />
            </IconButton>
          }
          fullWidth
        />
        <Modal.Root open={open} onOpenChange={(o) => setOpen(!!o)} dismissible>
          <Modal.Header>Modal Title</Modal.Header>
          <Modal.Content>
            <div>
              Once upon a time, there was a forest where plenty of birds lived
              and built their nests on the trees.
            </div>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={close}>Button</Button>
          </Modal.Footer>
          <Modal.CloseButton />
        </Modal.Root>
      </div>
    )
  },
}

/** Automplete with helper text */
export const HelperText: Story = {
  parameters: {
    docs: {
      description: {
        story: "Autocomplete with helper text and helper text decorator.",
      },
    },
  },
  render: (args) => {
    const [open, setOpen] = useState(false)
    const close = () => setOpen(false)
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
          gap: 20,
          alignItems: "center",
        }}
      >
        <Typography level="bodyLarge">Default</Typography>
        <Autocomplete
          {...args}
          label="Category"
          helperText="Helper text"
          placeholder="Placeholder text"
        />
        <Typography level="bodyLarge">With long helper text</Typography>
        <Autocomplete
          {...args}
          label="Category"
          placeholder="Select a category"
          error
          helperText={
            <Typography level="labelLarge" color="danger">
              You can not use the name
              <Typography
                level="labelLarge"
                color="danger"
                onClick={() => setOpen(true)}
                style={{
                  cursor: "pointer",
                  textDecoration: "underline",
                  marginLeft: "4px",
                }}
              >
                show detail
              </Typography>
            </Typography>
          }
          fullWidth
        />
        <Modal.Root open={open} onOpenChange={(o) => setOpen(!!o)} dismissible>
          <Modal.Header>Modal Title</Modal.Header>
          <Modal.Content>
            <div>
              Once upon a time, there was a forest where plenty of birds lived
              and built their nests on the trees.
            </div>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={close}>Button</Button>
          </Modal.Footer>
          <Modal.CloseButton />
        </Modal.Root>
      </div>
    )
  },
}

// Validation example
export const Validation: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Autocomplete with label, helper text, required and error (validation) states.",
      },
    },
  },
  render: (args) => (
    <div style={{ minWidth: 260 }}>
      <Autocomplete {...args} />
    </div>
  ),
  args: {
    label: "Category",
    required: true,
    helperText: "Validate me",
    error: true,
  },
}

export const SingleValue: Story = {
  render: (args) => {
    return <Autocomplete {...args} />
  },
  args: {
    label: "Label",
    helperText: "Helper text",
  },
}

export const SingleStates: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Common states side-by-side: default, disabled, error, and with helper/label.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
        gap: 20,
        alignItems: "end",
      }}
    >
      <Autocomplete
        {...args}
        label="Label"
        helperText="Helper text"
        placeholder="With label & helper"
      />
      <Autocomplete
        {...args}
        label="Label"
        helperText="Helper text"
        labelDecorator={<InfoCircle size={12} />}
        helperTextDecorator={<InfoCircle size={12} />}
        placeholder="With label & helper text decorators"
      />
      <Autocomplete {...args} disabled placeholder="Disabled" />
      <Autocomplete
        {...args}
        disabled
        placeholder="Disabled with value"
        defaultValue={"option1"}
      />
      <Autocomplete
        {...args}
        error
        label="Label"
        helperText="This field is required"
        placeholder="Error state"
      />
      <Autocomplete
        {...args}
        error
        label="Label"
        helperText="This field is required"
        helperTextDecorator={<InfoCircle size={12} />}
        placeholder="Error state with decorator"
      />
    </div>
  ),
  args: {
    fullWidth: true,
  },
}

// Multiple selection examples
export const MultipleValues: Story = {
  render: (args) => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
          gap: 20,
          alignItems: "center",
        }}
      >
        <Typography level="bodyLarge">Default</Typography>
        <Autocomplete {...args} />
        <Typography level="bodyLarge">With Select All</Typography>
        <Autocomplete {...args} showSelectAll />
        <Typography level="bodyLarge">With Custom Select All Label</Typography>
        <Autocomplete {...args} showSelectAll selectAllText="Choose All" />
        <Typography level="bodyLarge">Hide Checkbox</Typography>
        <Autocomplete {...args} hideCheckbox />
        <Typography level="bodyLarge">With Menu Label</Typography>
        <Autocomplete {...args} hideCheckbox menuLabelText="Select multiple" />
      </div>
    )
  },
  args: { fullWidth: true, multiple: true, placeholder: "Select multiple" },
}

export const MultipleStates: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Common states side-by-side: default, disabled, error, and with helper/label.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
        gap: 20,
        alignItems: "end",
      }}
    >
      <Autocomplete
        {...args}
        label="Label"
        helperText="Helper text"
        placeholder="With label & helper"
      />
      <Autocomplete
        {...args}
        label="Label"
        helperText="Helper text"
        labelDecorator={<InfoCircle size={12} />}
        helperTextDecorator={<InfoCircle size={12} />}
        placeholder="With label & helper text decorators"
      />
      <Autocomplete {...args} disabled placeholder="Disabled" />
      <Autocomplete
        {...args}
        disabled
        placeholder="Disabled with value"
        defaultValue={["option1", "option3"]}
      />
      <Autocomplete
        {...args}
        error
        label="Label"
        helperText="This field is required"
        placeholder="Error state"
      />
      <Autocomplete
        {...args}
        error
        label="Label"
        helperText="This field is required"
        helperTextDecorator={<InfoCircle size={12} />}
        placeholder="Error state with decorator"
      />
    </div>
  ),
  args: {
    multiple: true,
    fullWidth: true,
  },
}

// Controlled examples for interactivity
export const Controlled: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Controlled examples for interactivity. The first example is single-select and the second is multiple-select.",
      },
    },
  },
  render: (args) => {
    const [value, setValue] = useState()
    const [values, setValues] = useState([])
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 20,
        }}
      >
        <Typography>Single</Typography>
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">{`Selected: ${value ?? "(none)"}`}</Typography>
          <Autocomplete
            {...args}
            value={value}
            onChange={(v) => setValue(v)}
            fullWidth
          />
          <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
            <Button size="small" onClick={() => setValue("option3")}>
              Home & Kitchen — Cookware Sets
            </Button>
            <Button size="small" onClick={() => setValue("option7")}>
              Beauty & Personal Care
            </Button>
            <Button size="small" onClick={() => setValue(undefined)}>
              Clear
            </Button>
          </div>
        </div>
        <Typography>Multiple</Typography>
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">{`Selected: ${values.join(", ") || "(none)"}`}</Typography>
          <Autocomplete
            {...args}
            multiple
            value={values}
            onChange={(v) => setValues(v ?? [])}
            fullWidth
          />
          <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
            <Button
              size="small"
              onClick={() => setValues(["option1", "option3"])}
            >
              Set 2 values
            </Button>
            <Button size="small" onClick={() => setValues([])}>
              Clear
            </Button>
          </div>
        </div>
      </div>
    )
  },
  args: {
    label: "Label",
  },
}

export const LoadingState: Story = {
  render: (args) => {
    return (
      <div style={{ minWidth: 300 }}>
        <Autocomplete {...args} />
      </div>
    )
  },
  args: { loading: true, label: "Loading State", fullWidth: true },
}

// Infinite loading simulation
export const InfiniteLoading: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Shows hasMore/loadingMore with a load-more row. Click to append more items.",
      },
    },
  },
  render: (args) => {
    const initial = useMemo(() => baseOptions.slice(0, 4), [])
    const [opts, setOpts] = useState(initial)
    const [loadingMore, setLoadingMore] = useState(false)
    const [hasMore, setHasMore] = useState(true)

    const onLoadMore = async () => {
      if (!hasMore) return
      setLoadingMore(true)
      await new Promise((r) => setTimeout(r, 600))
      setOpts((prev) => {
        const nextIndex = prev.length
        const more = baseOptions.slice(nextIndex, nextIndex + 4)
        const combined = [...prev, ...more]
        if (combined.length >= baseOptions.length) setHasMore(false)
        return combined
      })
      setLoadingMore(false)
    }

    return (
      <div style={{ minWidth: 300 }}>
        <Autocomplete
          {...args}
          options={opts}
          hasMore={hasMore}
          loadingMore={loadingMore}
          onLoadMore={onLoadMore}
          loadMoreLabel={
            loadingMore ? "Loading more…" : hasMore ? "Load more" : "No more"
          }
        />
      </div>
    )
  },
  args: {
    label: "Infinite Loading",
    fullWidth: true,
  },
}

// Async search simulation
export const AsyncSearch: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates remote search using onSearch. Typing triggers a debounced async fetch and updates the option list.",
      },
    },
  },
  render: (args) => {
    const [opts, setOpts] = useState(baseOptions)
    const [loading, setLoading] = useState(false)

    const handleSearch = async (query: string) => {
      setLoading(true)
      // Simulate network latency
      await new Promise((resolve) => setTimeout(resolve, 500))
      const filtered = baseOptions.filter((option) =>
        option.label.toLowerCase().includes(query.toLowerCase())
      )
      setOpts(filtered)
      setLoading(false)
    }

    return (
      <div style={{ minWidth: 360 }}>
        <Autocomplete
          {...args}
          options={opts}
          onSearch={handleSearch}
          debounceMs={250}
          loading={loading}
          placeholder="Type to search…"
        />
      </div>
    )
  },
  args: {
    label: "Async Search",
    fullWidth: true,
  },
}

// Client-side search/filtering with case-insensitive matching and no-results UI
export const SearchFiltering: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Type in the menu's search box to filter options client-side. This example uses a custom filterLogic for case-insensitive matching and renders a no-results message when nothing matches.",
      },
    },
  },
  render: (args) => {
    const searchOptions = useMemo(
      () => [
        { label: "Apple", value: "apple" },
        { label: "apricot", value: "apricot" },
        { label: "BANANA", value: "banana" },
        { label: "Blueberry", value: "blueberry" },
        { label: "cherry", value: "cherry" },
        { label: "Dragon fruit", value: "dragonfruit" },
        { label: "Grape", value: "grape" },
        { label: "Kiwi", value: "kiwi" },
      ],
      []
    )

    // Case-insensitive filter
    const filterLogic = (
      options: Array<{ label: string; value: any }>,
      search: string
    ) => {
      const query = (search ?? "").trim().toLowerCase()
      if (!query) return options
      return options.filter((option) =>
        option.label.toLowerCase().includes(query)
      )
    }

    return (
      <div style={{ minWidth: 360 }}>
        <Autocomplete
          {...args}
          options={searchOptions}
          filterLogic={filterLogic}
          noOptionsComponent={
            <div style={{ padding: 8 }}>No results found</div>
          }
          placeholder="Type to filter (try: 'ap', 'BAN', 'berry')"
        />
      </div>
    )
  },
  args: {
    label: "Search (client-side)",
    helperText:
      "Case-insensitive filtering with real-time results and no-results message",
    debounceMs: 0,
  },
}

// Custom className styling examples
export const Customization: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates applying custom CSS classes via the className prop to alter borders, colors, and shape. Use the Controls panel to switch className on the first example.",
      },
    },
  },
  argTypes: {
    className: {
      control: { type: "select" },
      options: [
        "",
        "demo-autocomplete demo-autocomplete--secondary",
        "demo-autocomplete demo-autocomplete--warning",
      ],
      description:
        "Custom classes applied to the Autocomplete root element (see story-local styles).",
    },
  },
  render: (args) => {
    return (
      <>
        {/* Story-local demo styles for className variants */}
        <style>{`
          .demo-autocomplete--secondary { border-color: var(--apl-alias-color-secondary-secondary); }
          .demo-autocomplete--warning { border-color: var(--apl-alias-color-warning-warning); }
        `}</style>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
            gap: 24,
            alignItems: "center",
            minWidth: 640,
          }}
        >
          <Typography level="bodyLarge">Secondary Border</Typography>
          <Autocomplete
            {...args}
            className="demo-autocomplete demo-autocomplete--secondary"
            label="Primary"
            placeholder="Blue focus ring/border"
          />
          <Typography level="bodyLarge">Warning Border</Typography>
          <Autocomplete
            {...args}
            className="demo-autocomplete demo-autocomplete--warning"
            label="Warning"
            placeholder="Amber focus ring/border"
          />
        </div>
      </>
    )
  },
  args: {
    label: "Custom styles",
    helperText: "Using className to tweak borders, colors, and shape",
    className: "demo-autocomplete",
    fullWidth: true,
  },
}
