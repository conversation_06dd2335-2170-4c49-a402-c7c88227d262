import { useCallback, useState } from "react"
import {
  ComponentRules,
  CSSClassesTable,
  MultiplePropsTable,
  UsageGuidelines,
} from "@/components"
import { <PERSON>ton, Tabs, Typography } from "@apollo/ui"
import {
  <PERSON><PERSON>hart,
  CheckCircle,
  Dashboard,
  User,
} from "@design-systems/apollo-icons"
import {
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * Tabs component
 *
 * The Tabs component provides a tabbed interface for organizing content into
 * separate panels. It supports controlled and uncontrolled state, full-width
 * layouts, and disabled functionality. Built with accessibility in mind.
 *
 * Notes:
 * - Manages state for all Tab components within the group;
 * - Supports both controlled and uncontrolled usage patterns;
 * - Built on top of Base UI for accessibility.
 */
const meta = {
  title: "@apollo∕ui/Components/Layout/Tabs",
  component: Tabs.Root,
  subcomponents: {
    Tab: Tabs.Tab,
    List: Tabs.List,
    Panel: Tabs.Panel,
    Indicator: Tabs.Indicator,
  },
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2661-5721&m=dev",
    },
    docs: {
      description: {
        component:
          "The Tabs component provides a tabbed interface for organizing content into separate panels with Apollo design system styling. Supports controlled and uncontrolled state, full-width layouts, and disabled functionality. Built with accessibility in mind.",
      },

      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Tabs } from "@apollo/ui"`} language="tsx" />
          <h2 id="tabs-props">Props</h2>
          <MultiplePropsTable
            tabs={[
              {
                label: "Tabs.Root",
                props: [
                  {
                    name: "defaultValue",
                    description:
                      "The default selected tab value (uncontrolled mode).",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "value",
                    description:
                      "The currently selected tab value (controlled mode).",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "onValueChange",
                    description:
                      "Callback fired when the selected tab changes.",
                    type: "(value: string) => void",
                    defaultValue: "-",
                  },
                  {
                    name: "orientation",
                    description: "The orientation of the tabs.",
                    defaultValue: '"horizontal"',
                    type: '"horizontal" | "vertical"',
                  },
                  {
                    name: "children",
                    description:
                      "Tab components and panels to be rendered within the tabs.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "Tabs.List",
                props: [
                  {
                    name: "children",
                    description:
                      "Tab components and indicator to be rendered within the list.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "Tabs.Tab",
                props: [
                  {
                    name: "value",
                    description: "The value of the tab.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "disabled",
                    description: "Whether the tab is disabled.",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "variant",
                    description:
                      "Whether the tab should fit or fill its content width.",
                    defaultValue: "fit",
                    type: '"fit" | "fill"',
                  },
                  {
                    name: "align",
                    description: "Text alignment within the tab.",
                    defaultValue: '"center"',
                    type: '"left" | "center" | "right"',
                  },
                  {
                    name: "children",
                    description: "The content to display within the tab.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "Tabs.Indicator",
                props: [
                  {
                    name: "className",
                    description: "Additional CSS class names.",
                    type: "string",
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "Tabs.Panel",
                props: [
                  {
                    name: "value",
                    description: "The value that corresponds to the tab.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "keepMounted",
                    description:
                      "Whether to keep the panel in the DOM even when it's not active.",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "children",
                    description: "The content to display within the panel.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                ],
              },
            ]}
          />
          <h2 id="tabs-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use tabs to: Organize related content into separate, easily accessible sections",
              "Keep tab labels short and descriptive to clearly indicate the content within each panel",
              "Use a maximum of 5-7 tabs to avoid overwhelming users with too many options",
              "Ensure the first tab contains the most important or frequently accessed content",
              "Use consistent content structure across all tab panels for better user experience",
              "Avoid nesting tabs within tabs as it can create confusion in navigation hierarchy",
              "Use disabled state for tabs that are temporarily unavailable or require prerequisites",
              "Consider using full-width layout for better visual balance when you have few tabs",
            ]}
          />
          <h2 id="tabs-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide descriptive labels for tabs to ensure their
                purpose is clear to all users, including those using screen
                readers.
              </>,
              <>
                Use the <code>disabled</code> prop to disable tabs that are not
                currently actionable, ensuring they are not focusable.
              </>,
              <>
                Use <code>variant="fill"</code> prop in <code>Tabs.Tab</code> to
                make the content tabs fill the entire available width.
              </>,
            ]}
          />
          <h2 id="tabs-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Tabs component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloTabs-root",
                description: "Styles applied to the tabs root container",
                usageNotes: "Use for overall tabs styling and width control",
              },
              {
                cssClassName: ".ApolloTabs-list",
                description: "Styles applied to the tabs list container",
                usageNotes: "Contains flex layout for tab arrangement",
              },
              {
                cssClassName: ".ApolloTabs-tab",
                description: "Styles applied to individual tab elements",
                usageNotes:
                  "Contains tab styling including padding, typography, and states",
              },
              {
                cssClassName: ".ApolloTabs-indicator",
                description: "Styles applied to the active tab indicator",
                usageNotes:
                  "Provides the animated underline for the active tab",
              },
              {
                cssClassName: ".ApolloTabs-panel",
                description: "Styles applied to the tab panel elements",
                usageNotes:
                  "Contains panel styling including padding and display",
              },
            ]}
          />

          <h2 id="tabs-examples">Examples</h2>
          <Stories title="" />
          <h2 id="tabs-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ width: 400 }}>
                      <Tabs.Root defaultValue="overview">
                        <Tabs.List>
                          <Tabs.Tab value="overview">Overview</Tabs.Tab>
                          <Tabs.Tab value="features">Features</Tabs.Tab>
                          <Tabs.Tab value="pricing">Pricing</Tabs.Tab>
                          <Tabs.Indicator />
                        </Tabs.List>
                        <Tabs.Panel value="overview">
                          Product overview content
                        </Tabs.Panel>
                        <Tabs.Panel value="features">
                          Features and capabilities
                        </Tabs.Panel>
                        <Tabs.Panel value="pricing">
                          Pricing information
                        </Tabs.Panel>
                      </Tabs.Root>
                    </div>
                  ),
                  description:
                    "Use clear, descriptive labels that explain what content each tab contains",
                },
                negative: {
                  component: (
                    <div style={{ width: 400 }}>
                      <Tabs.Root defaultValue="tab1">
                        <Tabs.List>
                          <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
                          <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
                          <Tabs.Tab value="tab3">Tab 3</Tabs.Tab>
                          <Tabs.Indicator />
                        </Tabs.List>
                        <Tabs.Panel value="tab1">Content 1</Tabs.Panel>
                        <Tabs.Panel value="tab2">Content 2</Tabs.Panel>
                        <Tabs.Panel value="tab3">Content 3</Tabs.Panel>
                      </Tabs.Root>
                    </div>
                  ),
                  description:
                    "Avoid generic labels that don't indicate what content the tab contains",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ width: 400 }}>
                      <Tabs.Root defaultValue="dashboard">
                        <Tabs.List>
                          <Tabs.Tab value="dashboard">Dashboard</Tabs.Tab>
                          <Tabs.Tab value="analytics">Analytics</Tabs.Tab>
                          <Tabs.Tab value="settings">Settings</Tabs.Tab>
                          <Tabs.Indicator />
                        </Tabs.List>
                        <Tabs.Panel value="dashboard">
                          Dashboard content
                        </Tabs.Panel>
                        <Tabs.Panel value="analytics">
                          Analytics content
                        </Tabs.Panel>
                        <Tabs.Panel value="settings">
                          Settings content
                        </Tabs.Panel>
                      </Tabs.Root>
                    </div>
                  ),
                  description:
                    "Keep the number of tabs reasonable (3-5 tabs) for better usability",
                },
                negative: {
                  component: (
                    <div style={{ width: 700, padding: 8 }}>
                      <Tabs.Root defaultValue="tab1">
                        <Tabs.List>
                          <Tabs.Tab value="tab1" variant="fill">
                            Lorem ipsum dolor sit amet consectetur adipiscing
                            elit , sed do
                          </Tabs.Tab>
                          <Tabs.Tab value="tab2" variant="fill">Tab 2</Tabs.Tab>
                          <Tabs.Tab value="tab3" variant="fill">Tab 3</Tabs.Tab>
                          <Tabs.Indicator />
                        </Tabs.List>
                        <Tabs.Panel value="tab1">Content 1</Tabs.Panel>
                        <Tabs.Panel value="tab2">Content 2</Tabs.Panel>
                        <Tabs.Panel value="tab3">Content 3</Tabs.Panel>
                      </Tabs.Root>
                    </div>
                  ),
                  description:
                    "Avoid long label text that wraps to multiple lines",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    defaultValue: {
      control: { type: "text" },
      description: "The default selected tab value (uncontrolled mode).",
      table: {
        type: { summary: "string" },
      },
    },
    value: {
      control: { type: "text" },
      description: "The currently selected tab value (controlled mode).",
      table: {
        type: { summary: "string" },
      },
    },
    orientation: {
      control: { type: "select" },
      options: ["horizontal", "vertical"],
      description: "The orientation of the tabs.",
      table: {
        type: { summary: '"horizontal" | "vertical"' },
        defaultValue: { summary: '"horizontal"' },
      },
    },
    onValueChange: {
      control: false,
      description: "Callback fired when the selected tab changes.",
      table: {
        type: {
          summary: "(value: string) => void",
        },
      },
    },
    children: {
      control: false,
      description: "Tab components and panels to be rendered within the tabs.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: {
        type: { summary: "string" },
      },
    },
  },
  args: {
    defaultValue: "tab1",
  },
} satisfies Meta<typeof Tabs.Root>

export default meta

type Story = StoryObj<typeof Tabs.Root>

/** Default Tabs (demonstrates basic functionality) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Tabs with default settings. Shows a horizontal group of tabs with one tab pre-selected.",
      },
    },
  },
  render: (args) => (
    <Tabs.Root {...args}>
      <Tabs.List>
        <Tabs.Tab value="tab1">Overview</Tabs.Tab>
        <Tabs.Tab value="tab2">Features</Tabs.Tab>
        <Tabs.Tab value="tab3">Pricing</Tabs.Tab>
        <Tabs.Indicator />
      </Tabs.List>
      <Tabs.Panel value="tab1">
        <div style={{ padding: 16 }}>
          <Typography level="bodyLarge">Overview Content</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            This is the overview panel with important information about the
            product.
          </Typography>
        </div>
      </Tabs.Panel>
      <Tabs.Panel value="tab2">
        <div style={{ padding: 16 }}>
          <Typography level="bodyLarge">Features Content</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Detailed information about product features and capabilities.
          </Typography>
        </div>
      </Tabs.Panel>
      <Tabs.Panel value="tab3">
        <div style={{ padding: 16 }}>
          <Typography level="bodyLarge">Pricing Content</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Pricing plans and subscription information.
          </Typography>
        </div>
      </Tabs.Panel>
    </Tabs.Root>
  ),
}

/** Tabs with different variants */
export const Variants: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of all Tabs.Tab variants including default, full-width",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "1fr",
          gap: 32,
          alignItems: "flex-start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Default Tabs
          </Typography>
          <Tabs.Root defaultValue="home">
            <Tabs.List>
              <Tabs.Tab value="home">Home</Tabs.Tab>
              <Tabs.Tab value="about">About</Tabs.Tab>
              <Tabs.Tab value="contact">Contact</Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
            <Tabs.Panel value="home">
              <div style={{ padding: 16 }}>Home content</div>
            </Tabs.Panel>
            <Tabs.Panel value="about">
              <div style={{ padding: 16 }}>About content</div>
            </Tabs.Panel>
            <Tabs.Panel value="contact">
              <div style={{ padding: 16 }}>Contact content</div>
            </Tabs.Panel>
          </Tabs.Root>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Full Width Tabs
          </Typography>
          <Tabs.Root defaultValue="dashboard">
            <Tabs.List>
              <Tabs.Tab value="dashboard" variant="fill">
                Dashboard
              </Tabs.Tab>
              <Tabs.Tab value="analytics" variant="fill">
                Analytics
              </Tabs.Tab>
              <Tabs.Tab value="reports" variant="fill">
                Reports
              </Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
            <Tabs.Panel value="dashboard">
              <div style={{ padding: 16 }}>Dashboard content</div>
            </Tabs.Panel>
            <Tabs.Panel value="analytics">
              <div style={{ padding: 16 }}>Analytics content</div>
            </Tabs.Panel>
            <Tabs.Panel value="reports">
              <div style={{ padding: 16 }}>Reports content</div>
            </Tabs.Panel>
          </Tabs.Root>
        </div>
      </div>
    )
  },
}

/** Tabs with different states and layouts */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of all Tabs states including default, disabled, full-width, and fit-content variations.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "1fr",
          gap: 32,
          alignItems: "flex-start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Default Tabs
          </Typography>
          <Tabs.Root defaultValue="home">
            <Tabs.List>
              <Tabs.Tab value="home">Home</Tabs.Tab>
              <Tabs.Tab value="about">About</Tabs.Tab>
              <Tabs.Tab value="contact">Contact</Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
            <Tabs.Panel value="home">
              <div style={{ padding: 16 }}>Home content</div>
            </Tabs.Panel>
            <Tabs.Panel value="about">
              <div style={{ padding: 16 }}>About content</div>
            </Tabs.Panel>
            <Tabs.Panel value="contact">
              <div style={{ padding: 16 }}>Contact content</div>
            </Tabs.Panel>
          </Tabs.Root>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Full Width Tabs
          </Typography>
          <Tabs.Root defaultValue="dashboard">
            <Tabs.List>
              <Tabs.Tab value="dashboard" variant="fill">
                Dashboard
              </Tabs.Tab>
              <Tabs.Tab value="analytics" variant="fill">
                Analytics
              </Tabs.Tab>
              <Tabs.Tab value="reports" variant="fill">
                Reports
              </Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
            <Tabs.Panel value="dashboard">
              <div style={{ padding: 16 }}>Dashboard content</div>
            </Tabs.Panel>
            <Tabs.Panel value="analytics">
              <div style={{ padding: 16 }}>Analytics content</div>
            </Tabs.Panel>
            <Tabs.Panel value="reports">
              <div style={{ padding: 16 }}>Reports content</div>
            </Tabs.Panel>
          </Tabs.Root>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Disabled State
          </Typography>
          <Tabs.Root defaultValue="available">
            <Tabs.List>
              <Tabs.Tab value="available">Available</Tabs.Tab>
              <Tabs.Tab value="disabled" disabled>
                Disabled
              </Tabs.Tab>
              <Tabs.Tab value="active">Active</Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
            <Tabs.Panel value="available">
              <div style={{ padding: 16 }}>Available content</div>
            </Tabs.Panel>
            <Tabs.Panel value="disabled">
              <div style={{ padding: 16 }}>This should not be visible</div>
            </Tabs.Panel>
            <Tabs.Panel value="active">
              <div style={{ padding: 16 }}>Active content</div>
            </Tabs.Panel>
          </Tabs.Root>
        </div>
      </div>
    )
  },
}

/** Controlled Tabs example */
export const Controlled: Story = {
  parameters: {
    layout: "centered",
    docs: {
      description: {
        story:
          "A controlled Tabs component that manages its own state with React hooks.",
      },
    },
  },
  render: () => {
    function ControlledDemo() {
      const [selectedTab, setSelectedTab] = useState("profile")

      const handleChange = useCallback((value: string) => {
        setSelectedTab(value)
      }, [])

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 16,
            alignItems: "flex-start",
          }}
        >
          <Tabs.Root value={selectedTab} onValueChange={handleChange}>
            <Tabs.List>
              <Tabs.Tab value="profile">Profile</Tabs.Tab>
              <Tabs.Tab value="settings">Settings</Tabs.Tab>
              <Tabs.Tab value="notifications">Notifications</Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
            <Tabs.Panel value="profile">
              <div style={{ padding: 16 }}>
                <Typography level="bodyLarge">Profile Settings</Typography>
                <Typography level="bodyMedium" style={{ marginTop: 8 }}>
                  Manage your profile information and preferences.
                </Typography>
              </div>
            </Tabs.Panel>
            <Tabs.Panel value="settings">
              <div style={{ padding: 16 }}>
                <Typography level="bodyLarge">Account Settings</Typography>
                <Typography level="bodyMedium" style={{ marginTop: 8 }}>
                  Configure your account settings and security options.
                </Typography>
              </div>
            </Tabs.Panel>
            <Tabs.Panel value="notifications">
              <div style={{ padding: 16 }}>
                <Typography level="bodyLarge">
                  Notification Preferences
                </Typography>
                <Typography level="bodyMedium" style={{ marginTop: 8 }}>
                  Customize how and when you receive notifications.
                </Typography>
              </div>
            </Tabs.Panel>
          </Tabs.Root>
          <Typography level="bodySmall" style={{ color: "#6b7280" }}>
            Selected: {selectedTab}
          </Typography>
        </div>
      )
    }
    return <ControlledDemo />
  },
}

/** Tabs form integration example */
export const FormIntegration: Story = {
  parameters: {
    layout: "centered",
    docs: {
      description: {
        story:
          "Example of Tabs components integrated into a real-world form with submit functionality and dynamic content.",
      },
    },
  },
  render: () => {
    function FormDemo() {
      const [formData, setFormData] = useState({
        activeTab: "personal",
        personalInfo: { name: "", email: "" },
        preferences: { theme: "light", notifications: true },
        billing: { plan: "basic", paymentMethod: "credit" },
      })
      const [isSubmitting, setIsSubmitting] = useState(false)
      const [lastSubmitted, setLastSubmitted] = useState<
        typeof formData | null
      >(null)

      const handleTabChange = useCallback((value: string) => {
        setFormData((prev) => ({ ...prev, activeTab: value }))
      }, [])

      const handleSubmit = useCallback(
        async (e: React.FormEvent) => {
          e.preventDefault()
          setIsSubmitting(true)

          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000))

          setLastSubmitted({ ...formData })
          setIsSubmitting(false)
        },
        [formData]
      )

      return (
        <form
          onSubmit={handleSubmit}
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 24,
            maxWidth: 500,
          }}
        >
          <Tabs.Root value={formData.activeTab} onValueChange={handleTabChange}>
            <Tabs.List>
              <Tabs.Tab value="personal">Personal</Tabs.Tab>
              <Tabs.Tab value="preferences">Preferences</Tabs.Tab>
              <Tabs.Tab value="billing">Billing</Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>

            <Tabs.Panel value="personal">
              <div style={{ padding: 20 }}>
                <Typography
                  level="bodyLarge"
                  style={{ fontWeight: "600", marginBottom: 16 }}
                >
                  Personal Information
                </Typography>
                <div
                  style={{ display: "flex", flexDirection: "column", gap: 12 }}
                >
                  <Typography level="bodyMedium">Name: John Doe</Typography>
                  <Typography level="bodyMedium">
                    Email: <EMAIL>
                  </Typography>
                  <Typography level="bodyMedium">
                    Phone: +****************
                  </Typography>
                </div>
              </div>
            </Tabs.Panel>

            <Tabs.Panel value="preferences">
              <div style={{ padding: 20 }}>
                <Typography
                  level="bodyLarge"
                  style={{ fontWeight: "600", marginBottom: 16 }}
                >
                  User Preferences
                </Typography>
                <div
                  style={{ display: "flex", flexDirection: "column", gap: 12 }}
                >
                  <Typography level="bodyMedium">
                    Theme: {formData.preferences.theme}
                  </Typography>
                  <Typography level="bodyMedium">
                    Notifications:{" "}
                    {formData.preferences.notifications
                      ? "Enabled"
                      : "Disabled"}
                  </Typography>
                  <Typography level="bodyMedium">Language: English</Typography>
                </div>
              </div>
            </Tabs.Panel>

            <Tabs.Panel value="billing">
              <div style={{ padding: 20 }}>
                <Typography
                  level="bodyLarge"
                  style={{ fontWeight: "600", marginBottom: 16 }}
                >
                  Billing Information
                </Typography>
                <div
                  style={{ display: "flex", flexDirection: "column", gap: 12 }}
                >
                  <Typography level="bodyMedium">
                    Plan: {formData.billing.plan}
                  </Typography>
                  <Typography level="bodyMedium">
                    Payment Method: {formData.billing.paymentMethod}
                  </Typography>
                  <Typography level="bodyMedium">
                    Next Billing: March 15, 2024
                  </Typography>
                </div>
              </div>
            </Tabs.Panel>
          </Tabs.Root>

          <div style={{ borderTop: "1px solid #e5e7eb", paddingTop: 16 }}>
            <Button
              type="submit"
              disabled={isSubmitting}
              style={{ marginBottom: 12 }}
            >
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>

            <Typography level="bodySmall" style={{ color: "#6b7280" }}>
              Current tab: {formData.activeTab}
            </Typography>

            {lastSubmitted && (
              <Typography
                level="bodySmall"
                style={{ color: "#10b981", marginTop: 8 }}
              >
                ✓ Changes saved successfully!
              </Typography>
            )}
          </div>
        </form>
      )
    }
    return <FormDemo />
  },
}

/** Custom content with rich layouts */
export const CustomContent: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Tabs with custom content using rich layouts, icons, and complex structures. The children prop accepts ReactNode, allowing for flexible panel designs.",
      },
    },
  },
  render: () => {
    function CustomContentDemo() {
      const [selectedTab, setSelectedTab] = useState("dashboard")

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 32,
          }}
        >
          <div>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 16 }}
            >
              Project Management Dashboard
            </Typography>

            <Tabs.Root value={selectedTab} onValueChange={setSelectedTab}>
              <Tabs.List>
                <Tabs.Tab value="dashboard" variant="fill">
                  <span
                    role="img"
                    aria-label="dashboard"
                    style={{ marginRight: 4 }}
                  >
                    <Dashboard />
                  </span>
                  Dashboard
                </Tabs.Tab>
                <Tabs.Tab value="tasks" variant="fill">
                  <span
                    role="img"
                    aria-label="tasks"
                    style={{ marginRight: 4 }}
                  >
                    <CheckCircle />
                  </span>
                  Tasks
                </Tabs.Tab>
                <Tabs.Tab value="team" variant="fill">
                  <span role="img" aria-label="team" style={{ marginRight: 4 }}>
                    <User />
                  </span>
                  Team
                </Tabs.Tab>
                <Tabs.Tab value="analytics" variant="fill">
                  <span
                    role="img"
                    aria-label="analytics"
                    style={{ marginRight: 4 }}
                  >
                    <AreaChart />
                  </span>
                  Analytics
                </Tabs.Tab>
                <Tabs.Indicator />
              </Tabs.List>

              <Tabs.Panel value="dashboard">
                <div
                  style={{
                    padding: 24,
                    backgroundColor: "#f9fafb",
                    borderRadius: 8,
                  }}
                >
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns:
                        "repeat(auto-fit, minmax(200px, 1fr))",
                      gap: 16,
                    }}
                  >
                    <div
                      style={{
                        padding: 16,
                        backgroundColor: "white",
                        borderRadius: 6,
                        border: "1px solid #e5e7eb",
                      }}
                    >
                      <Typography
                        level="bodyLarge"
                        style={{ fontWeight: "600" }}
                      >
                        Active Projects
                      </Typography>
                      <Typography
                        level="displayLarge"
                        style={{ color: "#3b82f6", marginTop: 8 }}
                      >
                        12
                      </Typography>
                    </div>
                    <div
                      style={{
                        padding: 16,
                        backgroundColor: "white",
                        borderRadius: 6,
                        border: "1px solid #e5e7eb",
                      }}
                    >
                      <Typography
                        level="bodyLarge"
                        style={{ fontWeight: "600" }}
                      >
                        Completed Tasks
                      </Typography>
                      <Typography
                        level="displayLarge"
                        style={{ color: "#10b981", marginTop: 8 }}
                      >
                        247
                      </Typography>
                    </div>
                    <div
                      style={{
                        padding: 16,
                        backgroundColor: "white",
                        borderRadius: 6,
                        border: "1px solid #e5e7eb",
                      }}
                    >
                      <Typography
                        level="bodyLarge"
                        style={{ fontWeight: "600" }}
                      >
                        Team Members
                      </Typography>
                      <Typography
                        level="displayLarge"
                        style={{ color: "#f59e0b", marginTop: 8 }}
                      >
                        8
                      </Typography>
                    </div>
                  </div>
                </div>
              </Tabs.Panel>

              <Tabs.Panel value="tasks">
                <div style={{ padding: 24 }}>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      gap: 12,
                    }}
                  >
                    {[
                      "Design new landing page",
                      "Implement user authentication",
                      "Write API documentation",
                      "Review pull requests",
                    ].map((task, index) => (
                      <div
                        key={index}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: 12,
                          padding: 12,
                          backgroundColor: "#f9fafb",
                          borderRadius: 6,
                        }}
                      >
                        <span style={{ color: "#10b981" }}>✓</span>
                        <Typography level="bodyMedium">{task}</Typography>
                      </div>
                    ))}
                  </div>
                </div>
              </Tabs.Panel>

              <Tabs.Panel value="team">
                <div style={{ padding: 24 }}>
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns:
                        "repeat(auto-fit, minmax(150px, 1fr))",
                      gap: 16,
                    }}
                  >
                    {[
                      "Alice Johnson",
                      "Bob Smith",
                      "Carol Davis",
                      "David Wilson",
                    ].map((name, index) => (
                      <div
                        key={index}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          padding: 16,
                          backgroundColor: "#f9fafb",
                          borderRadius: 8,
                        }}
                      >
                        <div
                          style={{
                            width: 40,
                            height: 40,
                            borderRadius: "50%",
                            backgroundColor: "#3b82f6",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            color: "white",
                            marginBottom: 8,
                          }}
                        >
                          {name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </div>
                        <Typography
                          level="bodyMedium"
                          style={{ fontWeight: "500" }}
                        >
                          {name}
                        </Typography>
                      </div>
                    ))}
                  </div>
                </div>
              </Tabs.Panel>

              <Tabs.Panel value="analytics">
                <div style={{ padding: 24 }}>
                  <Typography
                    level="bodyLarge"
                    style={{ fontWeight: "600", marginBottom: 16 }}
                  >
                    Project Analytics
                  </Typography>
                  <div
                    style={{
                      height: 200,
                      backgroundColor: "#f9fafb",
                      borderRadius: 8,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      border: "2px dashed #d1d5db",
                    }}
                  >
                    <Typography level="bodyMedium" style={{ color: "#6b7280" }}>
                      Chart visualization would go here
                    </Typography>
                  </div>
                </div>
              </Tabs.Panel>
            </Tabs.Root>
          </div>

          <div style={{ borderTop: "1px solid #e5e7eb", paddingTop: 16 }}>
            <Typography level="bodySmall" style={{ color: "#6b7280" }}>
              Current View:{" "}
              {selectedTab.charAt(0).toUpperCase() + selectedTab.slice(1)}
            </Typography>
          </div>
        </div>
      )
    }
    return <CustomContentDemo />
  },
}
