import { Meta } from "@storybook/addon-docs/blocks"

<Meta title="Contributing" tags={["new"]} />

# Contributing Guide

Welcome to the Apollo Design System contributing guide! This comprehensive resource is designed specifically for UX/UI team members who want to contribute to the design system.

<div style={{ lineHeight: "1.8" }}>

- [Design System Process](#design-system-process)
- [Design Request Workflows](#design-request-workflows)
- [Design Review and Approval Workflows](#design-review-and-approval-workflows)

</div>

## Design System Process

This document outlines the process for submitting and handling requests for updates within the Apollo Design System project.

<div
  style={{
    textAlign: "center",
    border: "1px solid #d1d1d1",
    borderRadius: "8px",
    padding: "16px",
  }}
>
  <img src="/process/overall.png" alt="Overall Workflow" />
</div>

<br />
<br />

## Design Request Workflows

To make team member noticed of the request, please follow these steps:

Any team member of UX/UI can submit a request for updates or changes to the design system. Before the update will be accepted, the request must be reviewed and approved by another UX/UI team member.

1. **Create the Request Issue**: For new features, suggestions, or bugs, please make sure to [create an issue](https://apollo-issue-apollo-issue.vercel.app/issues/new) first with the details of the request.

        <div style={{ textAlign: "center", border: "1px solid #d1d1d1", borderRadius: "8px", padding: "16px" }}><img src="/process/create-issue.png" alt="Create Issue" /></div>

        **Template for request:**

        ```
        ----

        Title: [Short description of the request]
        Description: [Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.]
        Priority:[ Low | Medium | High | Urgent ]
        Due Date: Date
        Review Due Date: Date

        Issue Link: LINK

        ---

        ✅ Approved ❌ Declined

        ```

2. **Post the detail of changes**: The request should include a summary detailed description of the changes needed, the reason for the change, and any relevant context or examples. Post in the MS Team at `[Design System] Design Library & Issues`

        <div style={{ textAlign: "center", border: "1px solid #d1d1d1", borderRadius: "8px", padding: "16px" }}><img src="/process/post.png" alt="Process Example" /></div>

3. **Backlog/Planned**: Once the request is approved, it will be added to the project backlog. The team will prioritize the request based on its importance and urgency.

         <div style={{ textAlign: "center", border: "1px solid #d1d1d1", borderRadius: "8px", padding: "16px" }}><img src="/process/approved.png" alt="Approved" /></div>


<br />
<br />

## Design Review and Approval Workflows

To make sure that implemented code aligned with Figma Design. We needed the confirmation from the Designer.

### Review Process Steps

1. **Create implementation**: The UI-Engineer develop the component or token changes
2. **Submit for review**: The UI-Engineer create a pull request with changes
3. **Chromatic review**: Automated visual testing will generate review links
4. **Designer notification**: The UI-Engineer will notify the Designer in the MS Team at `[Design System] Design Library & Issues` channel with the review link.

Example: [Chromatic](https://www.chromatic.com/review?appId=68ac0015b0397b0b5a740fcd&number=1&type=unlinked&view=activity)

<div style={{ textAlign: "center", border: "1px solid #d1d1d1", borderRadius: "8px", padding: "16px" }}><img src="/process/process-exam.png" alt="Process Example" /></div>

5. **Approval**: Wait for designer approval before merging
6. **Release planning**: The UI-Engineer will set the target release version in the issue to indicate when the changes are expected to be deployed.

<div style={{ textAlign: "center", border: "1px solid #d1d1d1", borderRadius: "8px", padding: "16px" }}><img src="/process/complete.png" alt="Process Example" /></div>

7. **Release**: Once the changes have been reviewed and approved, they will be released to the design system. After the release, Announce the release in the MS Team.

### Chromatic Integration

The design system uses Chromatic for visual testing and design review:

- **Automatic builds**: Every pull request triggers a Chromatic build
- **Visual diff**: Chromatic shows visual changes between versions
- **Review links**: Share Chromatic links with designers for approval
- **Approval workflow**: Designers can approve or request changes directly in Chromatic

#### Example Review Request Message

```
🔍 Design Review Request

Component: Button Component Updates
MR: https://gitlab.com/apollo/pull/123
Chromatic: https://chromatic.com/review?appId=abc&number=456

Changes:
- Added new "ghost" variant
- Updated focus states for accessibility
- Fixed spacing inconsistencies

@designer-name
Please review and approve when ready. Target release: v2.1.0
```

### Approval Process

1. **Initial Review**: Designer reviews visual implementation
2. **Feedback Incorporation**: Address any design feedback
3. **Technical Review**: Code review by engineering team
4. **Final Approval**: Designer gives final approval
5. **Merge and Release**: Component is merged and scheduled for release

### Release Planning

After approval, components go through release planning:

1. **Version Assignment**: Assign to appropriate release version
2. **Release Notes**: Document changes in release notes
3. **Migration Guide**: Update migration documentation if needed
4. **Communication**: Announce release to stakeholders

---

The Contributing Guide complements this process documentation by providing the technical details needed to implement approved changes effectively.

#### We really appreciate your contribution to Apollo! 🚀