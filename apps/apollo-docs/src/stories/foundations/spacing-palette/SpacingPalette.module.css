.spacingPalette {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.searchContainer {
  margin-bottom: 32px;
}

.spacingGroups {
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.spacingGroup {
  width: 100%;
}

.spacingGroupTitle {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  border-bottom: 2px solid var(--apl-alias-color-outline-and-border-outline);
  padding-bottom: 8px;
}

.spacingSubgroup {
  margin-bottom: 32px;
}

.spacingSubgroupTitle {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--apl-alias-color-background-and-surface-on-surface);
}

.spacingGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.spacingSwatch {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: var(--apl-alias-color-background-and-surface-surface);
  border: 1px solid var(--apl-alias-color-outline-outline-variant);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  position: relative;
  min-height: 120px;
}

.spacingSwatch:hover {
  border-color: var(--apl-alias-color-primary-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.spacingSwatch:focus {
  outline: 2px solid var(--apl-alias-color-primary-primary);
  outline-offset: 2px;
}

.spacingSwatchPreview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  min-height: 48px;
  justify-content: center;
}

.spacingContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 40px;
}

.spacingDemo {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--apl-alias-color-background-and-surface-surface-variant);
  border: 1px dashed var(--apl-alias-color-outline-outline-variant);
  border-radius: 6px;
  padding: 8px 16px;
  min-width: 160px;
  position: relative;
  gap: 0;
}

.spacingBox {
  width: 24px;
  height: 24px;
  background: var(--apl-alias-color-background-and-surface-surface);
  border: 1px solid var(--apl-alias-color-outline-outline-variant);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  flex-shrink: 0;
}

.spacingVisual {
  background: var(--apl-alias-color-primary-primary);
  height: 4px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1px;
  transition: all 0.2s ease;
  border-radius: 2px;
}

.spacingVisual.negativeSpacing {
  background: var(--apl-alias-color-error-error);
  border: 1px dashed var(--apl-alias-color-error-error);
  background-color: transparent;
  opacity: 0.8;
  height: 2px;
}

.spacingMeasurement {
  font-size: 12px;
  font-weight: 500;
  color: var(--apl-alias-color-background-and-surface-on-surface-variant);
  background: var(--apl-alias-color-background-and-surface-surface-variant);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--apl-alias-color-outline-outline-variant);
}

.spacingSwatchInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.spacingSwatchVariable {
  font-size: 14px;
  font-weight: 500;
  color: var(--apl-alias-color-primary-primary);
  word-break: break-all;
  line-height: 1.3;
}

.spacingSwatchName {
  font-size: 12px;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  font-weight: 500;
}

.spacingSwatchCategory {
  font-size: 10px;
  color: var(--apl-alias-color-primary-primary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: var(--apl-alias-color-primary-primary-container);
  padding: 2px 6px;
  border-radius: 4px;
  align-self: flex-start;
  margin-top: 4px;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  20% {
    opacity: 1;
    transform: scale(1);
  }
  80% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .spacingGrid {
    grid-template-columns: 1fr;
  }
  
  .spacingGroupTitle {
    font-size: 20px;
  }
  
  .spacingSubgroupTitle {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .spacingSwatch {
    padding: 12px;
    min-height: 100px;
  }
  
  .spacingSwatchVariable {
    font-size: 12px;
  }
  
  .spacingSwatchName {
    font-size: 11px;
  }
}
