import React, { use<PERSON>emo, useState } from "react"
import { Input, Theme } from "@apollo/ui"
import { CheckCircle } from "@design-systems/apollo-icons"

import styles from "./SpacingPalette.module.css"

interface SpacingToken {
  cssVariable: string
  pixelValue: string
  category: string
  subcategory?: string
  displayName: string
}

interface SpacingSwatchProps {
  token: SpacingToken
}

const SpacingSwatch: React.FC<SpacingSwatchProps> = ({ token }) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error("Failed to copy text: ", err)
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault()
      handleCopy(token.cssVariable)
    }
  }

  // Convert pixel value to number for visual representation
  const pixelNumber = parseInt(token.pixelValue.replace("px", ""))
  const isNegative = pixelNumber < 0
  const absolutePixelNumber = Math.abs(pixelNumber)

  return (
    <button
      className={styles.spacingSwatch}
      onClick={() => handleCopy(token.cssVariable)}
      onKeyDown={handleKeyDown}
      aria-label={`Copy ${token.cssVariable} to clipboard`}
      title={`Click to copy ${token.cssVariable}`}
    >
      <div className={styles.spacingSwatchPreview}>
        <div className={styles.spacingContainer}>
          <div className={styles.spacingDemo}>
            <div className={styles.spacingBox}>A</div>
            <div
              className={`${styles.spacingVisual} ${isNegative ? styles.negativeSpacing : ""}`}
              style={{
                width:
                  absolutePixelNumber === 0
                    ? "1px"
                    : `${Math.min(Math.max(absolutePixelNumber, 1), 80)}px`,
              }}
            />
            <div className={styles.spacingBox}>B</div>
          </div>
        </div>
        <div className={styles.spacingMeasurement}>{token.pixelValue}</div>
      </div>
      {copied && (
        <div
          style={{
            position: "absolute",
            top: "4px",
            right: "4px",
            display: "flex",
            alignItems: "center",
            gap: "4px",
            padding: "4px 8px",
            backgroundColor: "#10b981",
            color: "white",
            borderRadius: "4px",
            fontSize: "11px",
            fontWeight: "500",
          }}
        >
          <CheckCircle size={12} />
          Copied
        </div>
      )}
      <div className={styles.spacingSwatchInfo}>
        <div className={styles.spacingSwatchVariable}>{token.cssVariable}</div>
        <div className={styles.spacingSwatchName}>{token.displayName}</div>
      </div>
    </button>
  )
}

interface SpacingGroupProps {
  title: string
  tokens: SpacingToken[]
  searchTerm: string
}

const SpacingGroup: React.FC<SpacingGroupProps> = ({
  title,
  tokens,
  searchTerm,
}) => {
  const filteredTokens = useMemo(() => {
    if (!searchTerm) return tokens

    return tokens.filter(
      (token) =>
        token.cssVariable.toLowerCase().includes(searchTerm.toLowerCase()) ||
        token.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        token.pixelValue.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [tokens, searchTerm])

  // Group tokens by subcategory if they exist
  const groupedTokens = useMemo(() => {
    const groups: { [key: string]: SpacingToken[] } = {}

    filteredTokens.forEach((token) => {
      const key = token.subcategory || "default"
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(token)
    })

    return groups
  }, [filteredTokens])

  if (filteredTokens.length === 0) return null

  return (
    <div className={styles.spacingGroup}>
      <h2 id={title} className={styles.spacingGroupTitle}>
        {title}
      </h2>
      {Object.entries(groupedTokens).map(([subcategory, subTokens]) => (
        <div key={subcategory} className={styles.spacingSubgroup}>
          {subcategory !== "default" && (
            <h4 className={styles.spacingSubgroupTitle}>{subcategory}</h4>
          )}
          <div className={styles.spacingGrid}>
            {subTokens.map((token) => (
              <SpacingSwatch key={token.cssVariable} token={token} />
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

// Extract spacing tokens from CSS variables
const getSpacingTokens = (): {
  aliasTokens: SpacingToken[]
  baseTokens: SpacingToken[]
} => {
  const aliasTokens: SpacingToken[] = []
  const baseTokens: SpacingToken[] = []

  // Base spacing tokens
  const baseSpacingData = [
    { name: "space1", value: "0px", display: "Space 1 (None)" },
    { name: "space2", value: "2px", display: "Space 2" },
    { name: "space3", value: "4px", display: "Space 3" },
    { name: "space4", value: "6px", display: "Space 4" },
    { name: "space5", value: "8px", display: "Space 5" },
    { name: "space6", value: "10px", display: "Space 6" },
    { name: "space7", value: "12px", display: "Space 7" },
    { name: "space8", value: "16px", display: "Space 8" },
    { name: "space9", value: "20px", display: "Space 9" },
    { name: "space10", value: "24px", display: "Space 10" },
    { name: "space11", value: "28px", display: "Space 11" },
    { name: "space12", value: "32px", display: "Space 12" },
    { name: "space13", value: "36px", display: "Space 13" },
    { name: "space14", value: "40px", display: "Space 14" },
    { name: "space15", value: "48px", display: "Space 15" },
    { name: "neg-space1", value: "-2px", display: "Negative Space 1" },
    { name: "neg-space2", value: "-4px", display: "Negative Space 2" },
    { name: "neg-space3", value: "-6px", display: "Negative Space 3" },
    { name: "neg-space4", value: "-8px", display: "Negative Space 4" },
  ]

  baseSpacingData.forEach((item) => {
    baseTokens.push({
      cssVariable: `--apl-base-spacing-${item.name}`,
      pixelValue: item.value,
      category: "Base",
      displayName: item.display,
    })
  })

  // Alias spacing tokens
  const aliasSpacingData = [
    // Gap tokens
    { name: "gap1", value: "0px", display: "Gap 1", subcategory: "Gap" },
    { name: "gap2", value: "2px", display: "Gap 2", subcategory: "Gap" },
    { name: "gap3", value: "4px", display: "Gap 3", subcategory: "Gap" },
    { name: "gap4", value: "6px", display: "Gap 4", subcategory: "Gap" },
    { name: "gap5", value: "8px", display: "Gap 5", subcategory: "Gap" },
    { name: "gap6", value: "10px", display: "Gap 6", subcategory: "Gap" },
    { name: "gap7", value: "12px", display: "Gap 7", subcategory: "Gap" },
    { name: "gap8", value: "16px", display: "Gap 8", subcategory: "Gap" },
    { name: "gap9", value: "20px", display: "Gap 9", subcategory: "Gap" },
    { name: "gap10", value: "24px", display: "Gap 10", subcategory: "Gap" },
    { name: "gap11", value: "28px", display: "Gap 11", subcategory: "Gap" },
    { name: "gap12", value: "32px", display: "Gap 12", subcategory: "Gap" },

    // Padding tokens
    {
      name: "padding1",
      value: "0px",
      display: "Padding 1",
      subcategory: "Padding",
    },
    {
      name: "padding2",
      value: "2px",
      display: "Padding 2",
      subcategory: "Padding",
    },
    {
      name: "padding3",
      value: "4px",
      display: "Padding 3",
      subcategory: "Padding",
    },
    {
      name: "padding4",
      value: "6px",
      display: "Padding 4",
      subcategory: "Padding",
    },
    {
      name: "padding5",
      value: "8px",
      display: "Padding 5",
      subcategory: "Padding",
    },
    {
      name: "padding6",
      value: "10px",
      display: "Padding 6",
      subcategory: "Padding",
    },
    {
      name: "padding7",
      value: "12px",
      display: "Padding 7",
      subcategory: "Padding",
    },
    {
      name: "padding8",
      value: "16px",
      display: "Padding 8",
      subcategory: "Padding",
    },
    {
      name: "padding9",
      value: "20px",
      display: "Padding 9",
      subcategory: "Padding",
    },
    {
      name: "padding10",
      value: "24px",
      display: "Padding 10",
      subcategory: "Padding",
    },
    {
      name: "padding11",
      value: "28px",
      display: "Padding 11",
      subcategory: "Padding",
    },
    {
      name: "padding12",
      value: "32px",
      display: "Padding 12",
      subcategory: "Padding",
    },

    // Margin tokens
    {
      name: "horizontal-horizontal",
      value: "48px",
      display: "Horizontal Margin",
      subcategory: "Margin",
    },
    {
      name: "vertical-vertical",
      value: "48px",
      display: "Vertical Margin",
      subcategory: "Margin",
    },
  ]

  aliasSpacingData.forEach((item) => {
    let cssVariable = ""
    if (item.subcategory === "Gap") {
      cssVariable = `--apl-alias-spacing-gap-${item.name}`
    } else if (item.subcategory === "Padding") {
      cssVariable = `--apl-alias-spacing-padding-${item.name}`
    } else if (item.subcategory === "Margin") {
      cssVariable = `--apl-alias-spacing-margin-${item.name}`
    }

    aliasTokens.push({
      cssVariable,
      pixelValue: item.value,
      category: "Alias",
      subcategory: item.subcategory,
      displayName: item.display,
    })
  })

  return { aliasTokens, baseTokens }
}

export const SpacingPalette: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("")
  const { aliasTokens, baseTokens } = useMemo(() => getSpacingTokens(), [])

  return (
    <Theme>
      <div className={styles.spacingPalette}>
        <div className={styles.searchContainer}>
          <Input
            placeholder="Search spacing tokens..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            fullWidth
          />
        </div>

        <div className={styles.spacingGroups}>
          <SpacingGroup
            title="Alias Tokens"
            tokens={aliasTokens}
            searchTerm={searchTerm}
          />

          <SpacingGroup
            title="Base Tokens"
            tokens={baseTokens}
            searchTerm={searchTerm}
          />
        </div>
      </div>
    </Theme>
  )
}
