import { Meta } from "@storybook/addon-docs/blocks"
import { SpacingPalette } from "./spacing-palette"

<Meta title="Foundations/Spacing" tags={["docs"]} />

# Spacing

Spacing controls the empty areas around or within components, such as margins, padding, and gaps between elements. Effective spacing improves readability, reduces visual clutter, and enhances usability. 

The spacing system is organized into two main collections of variables in Figma [💙 Apollo Alias Foundations and Styles](https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=13-3&t=UZXeBdRDW7D0SWTy-1):

- **Alias tokens**: Semantic spacing mappings organized by usage roles (Gap, Padding, Margin, etc.), use reference spacing from `Base`.
- **Base tokens**: Foundational spacing primitives organized by size increments (space1, space2, etc.)

Use the search functionality to quickly find specific spacing values across both sections. The search will filter spacing tokens in real-time across all categories.

<SpacingPalette />

## Usage

### Recommended: Use Alias Spacing

For most use cases, use alias spacing tokens as they provide semantic meaning and consistent spacing:

```css
.card {
  padding: var(--apl-alias-spacing-padding-padding4);
  gap: var(--apl-alias-spacing-gap-gap2);
}

.layout {
  margin: var(--apl-alias-spacing-margin-vertical-vertical);
}

.button-group {
  gap: var(--apl-alias-spacing-gap-gap3);
}
```

## Spacing Categories

The spacing system includes several semantic categories:

- **Gap**: Use for spacing between elements in flexbox or grid layouts
- **Padding**: Use for internal spacing within components like buttons, cards, and containers
- **Margin**: Use for external spacing around components and layout sections

## Best Practices

- **Use alias tokens** for semantic spacing that adapts to design changes
- **Maintain consistency** by using the same spacing values throughout your design
- **Consider hierarchy** - larger spacing values create more visual separation
- **Test responsiveness** - ensure spacing works well across different screen sizes