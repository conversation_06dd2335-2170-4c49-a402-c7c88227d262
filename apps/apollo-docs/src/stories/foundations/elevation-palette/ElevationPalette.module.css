.elevationPalette {
  display: flex;
  flex-direction: column;
  gap: 32px;
  max-width: 100%;
}

.elevationGroups {
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.elevationGroup {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.elevationGroupTitle {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: var(--apl-alias-color-text-text);
  border-bottom: 1px solid var(--apl-alias-color-outline-and-border-outline);
  padding-bottom: 8px;
}

.elevationSubgroup {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.elevationSubgroupTitle {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  color: var(--apl-alias-color-background-and-surface-on-surface);
}

.elevationGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.elevationAliasGroupGrid {
  display: grid;
  grid-template-columns: 50% 50%;
  gap: 16px;
}

.elevationSwatch {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: var(--apl-alias-color-background-and-surface-surface);
  border: 1px solid var(--apl-alias-color-outline-and-border-outline);
  border-radius: var(--apl-alias-radius-radius3);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.elevationSwatch:hover {
  box-shadow: 0 4px 12px var(--apl-alias-color-effects-shadow);
  transform: translateY(-2px);
}

.elevationPreview {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
  border: 1px solid var(--apl-alias-color-outline-and-border-outline);
  padding: 16px;
  border-radius: var(--apl-alias-radius-radius3);
}

.elevationContainer {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--apl-alias-color-background-and-surface-background);
  border-radius: var(--apl-alias-radius-radius2);
  position: relative;
}

.elevationDemo {
  width: 100px;
  height: 100px;
  background: var(--apl-alias-color-background-and-surface-surface);
  border-radius: var(--apl-alias-radius-radius2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  color: var(--apl-alias-color-text-text);
  transition: all 0.2s ease;
}

.elevationProperties {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 14px;
  color: var(--apl-alias-color-background-and-surface-on-surface);
}

.elevationProperty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.elevationPropertyLabel {
  font-weight: 600;
  min-width: 50px;
}

.elevationPropertyLabelVariable {
  color: var(--apl-alias-color-primary-primary)
}

.elevationPropertyValue {
  text-align: right;
  word-break: break-all;
}

.elevationSwatchInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.elevationSwatchVariable {
  font-size: 14px;
  color: var(--apl-alias-color-primary-primary);
  word-break: break-all;
}

.elevationSwatchValue {
  font-size: 12px;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  font-weight: 600;
}

/* Elevation Section Styles */
.elevationSection {
  margin-bottom: 32px;
}

.elevationSectionTitle {
  font-size: 24px;
  font-weight: 600;
  color: var(--apl-alias-color-text-text);
  margin: 0 0 24px 0;
  border-bottom: 1px solid var(--apl-alias-color-outline-and-border-outline);
  padding-bottom: 8px;
}

/* Elevation Level Group Styles */
.elevationLevelGroup {
  overflow: hidden;
}

.elevationLevelGroupHeader {
  background: var(--apl-alias-color-background-and-surface-background);
}

.elevationLevelGroupTitle {
  font-size: 18px;
  font-weight: 600;
  color: var(--apl-alias-color-text-text);
  margin: 0 0 16px 0;
}

/* Override grid styles for elevation level groups */
.elevationLevelGroup .elevationGrid {
  padding: 16px;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

/* Smaller swatches for individual tokens in groups */
.elevationLevelGroup .elevationSwatch {
  padding: 12px;
  gap: 8px;
}
