import { Meta } from "@storybook/addon-docs/blocks"
import { BorderRadiusPalette } from "./border-radius-palette"

<Meta title="Foundations/Border Radius" tags={["docs"]} />

# Border Radius

Borders and radiuses are basic visual design elements that help create clarity, hierarchy, and a consistent look.

The border radius system is organized into two main collections of variables in Figma [💙 Apollo Alias Foundations and Styles](https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=13-3&t=UZXeBdRDW7D0SWTy-1):

- **Alias tokens**: Semantic border radius mappings organized by usage roles (radius1, radius2, etc.), use reference values from `Base`.
- **Base tokens**: Foundational border radius primitives organized by pixel values (none, 4px, 6px, etc.)

Use the search functionality to quickly find specific border radius values across both sections. The search will filter radius tokens in real-time across all categories.

<BorderRadiusPalette />

## Usage

### Recommended: Use Alias Border Radius

For most use cases, use alias border radius tokens as they provide semantic meaning and consistent styling:

```css
.card {
  border-radius: var(--apl-alias-radius-radius4);
}

.button {
  border-radius: var(--apl-alias-radius-radius2);
}

.avatar {
  border-radius: var(--apl-alias-radius-radius11); /* Full circle */
}

.input {
  border-radius: var(--apl-alias-radius-radius3);
}
```

## Border Radius Categories

The border radius system includes several semantic categories:

- **None (0px)**: Use for sharp, angular designs and when no rounding is desired
- **Small (2-6px)**: Use for subtle rounding on buttons, inputs, and small components
- **Medium (8-16px)**: Use for cards, modals, and medium-sized components
- **Large (20-24px)**: Use for prominent components and containers
- **Full (9999px)**: Use for circular elements like avatars, badges, and pills

## Best Practices

- **Use alias tokens** for semantic border radius that adapts to design changes
- **Maintain consistency** by using the same border radius values for similar components
- **Consider component hierarchy** - larger components can use larger border radius values
- **Match the design language** - consistent border radius helps establish visual identity
- **Test across platforms** - ensure border radius renders consistently across different browsers and devices