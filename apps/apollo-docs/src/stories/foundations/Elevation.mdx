import { Meta } from "@storybook/addon-docs/blocks"
import { ElevationPalette } from "./elevation-palette"

<Meta title="Foundations/Elevation" tags={["docs"]} />

# Elevation

Elevation creates visual hierarchy and depth through shadows and layering. The Apollo design system provides a comprehensive elevation system that uses CSS custom properties for consistent shadow effects across components.

The elevation system is organized into two main collections of variables in Figma [💙 Apollo Alias Foundations and Styles](https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=13-3&t=UZXeBdRDW7D0SWTy-1):

- **Alias tokens**: Semantic elevation mappings organized by usage roles (elevations1, elevations2, etc.), use reference values from `Base`
- **Base tokens**: Foundational elevation primitives organized by shadow properties (blur, spread, x-axis, y-axis)

Use the search functionality to quickly find specific elevation values across both sections. The search will filter elevation tokens in real-time across all categories.

<ElevationPalette />

## Usage

### Recommended: Use Alias Elevation

For most use cases, use alias elevation tokens as they provide semantic meaning and consistent shadow effects:

```css
.card {
  box-shadow:
    var(--apl-alias-elevation-elevations1-x-axis)
    var(--apl-alias-elevation-elevations1-y-axis)
    var(--apl-alias-elevation-elevations1-blur)
    var(--apl-alias-elevation-elevations1-spread)
    var(--apl-alias-elevation-elevations1-color);
}
```

**Note:** Currently, only `elevations1` is actively defined with shadow values. `elevations2` and `elevations3` are set to zero values and are not in use. This provides room for future expansion of the elevation system.


## Elevation Properties

Elevation is composed of multiple shadow properties that work together:

- **X-axis**: Horizontal offset of the shadow
- **Y-axis**: Vertical offset of the shadow
- **Blur**: Blur radius of the shadow
- **Spread**: Spread radius of the shadow
- **Color**: Color and opacity of the shadow