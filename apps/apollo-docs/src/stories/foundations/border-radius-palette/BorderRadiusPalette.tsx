import React, { useMemo, useState } from "react"
import { Input, Theme } from "@apollo/ui"
import { CheckCircle } from "@design-systems/apollo-icons"

import styles from "./BorderRadiusPalette.module.css"

interface BorderRadiusToken {
  cssVariable: string
  pixelValue: string
  category: string
  displayName: string
}

interface BorderRadiusSwatchProps {
  token: BorderRadiusToken
}

const BorderRadiusSwatch: React.FC<BorderRadiusSwatchProps> = ({ token }) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error("Failed to copy text: ", err)
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault()
      handleCopy(token.cssVariable)
    }
  }

  const pixelNumber = parseFloat(token.pixelValue.replace("px", ""))
  const isFullRadius = token.pixelValue === "9999px"

  return (
    <button
      className={styles.borderRadiusSwatch}
      onClick={() => handleCopy(token.cssVariable)}
      onKeyDown={handleKeyDown}
      aria-label={`Copy ${token.cssVariable} to clipboard`}
      title={`Click to copy ${token.cssVariable}`}
    >
      <div className={styles.borderRadiusSwatchPreview}>
        <div className={styles.borderRadiusContainer}>
          <div
            className={styles.borderRadiusDemo}
            style={{
              borderRadius: isFullRadius
                ? "50%"
                : `${Math.min(pixelNumber, 24)}px`,
            }}
          >
            <div className={styles.borderRadiusContent}>
              {isFullRadius ? "●" : "Aa"}
            </div>
          </div>
        </div>
        <div className={styles.borderRadiusMeasurement}>{token.pixelValue}</div>
      </div>
      {copied && (
        <div
          style={{
            position: "absolute",
            top: "4px",
            right: "4px",
            display: "flex",
            alignItems: "center",
            gap: "4px",
            padding: "4px 8px",
            backgroundColor: "#10b981",
            color: "white",
            borderRadius: "4px",
            fontSize: "11px",
            fontWeight: "500",
          }}
        >
          <CheckCircle size={12} />
          Copied
        </div>
      )}
      <div className={styles.borderRadiusSwatchInfo}>
        <div className={styles.borderRadiusSwatchVariable}>
          {token.cssVariable}
        </div>
        <div className={styles.borderRadiusSwatchName}>{token.displayName}</div>
      </div>
    </button>
  )
}

interface BorderRadiusGroupProps {
  title: string
  tokens: BorderRadiusToken[]
  searchTerm: string
}

const BorderRadiusGroup: React.FC<BorderRadiusGroupProps> = ({
  title,
  tokens,
  searchTerm,
}) => {
  const filteredTokens = tokens.filter(
    (token) =>
      token.cssVariable.toLowerCase().includes(searchTerm.toLowerCase()) ||
      token.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      token.pixelValue.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (filteredTokens.length === 0) return null

  return (
    <div className={styles.borderRadiusGroup}>
      <h2 id={title} className={styles.borderRadiusGroupTitle}>
        {title}
      </h2>
      <div className={styles.borderRadiusGrid}>
        {filteredTokens.map((token) => (
          <BorderRadiusSwatch key={token.cssVariable} token={token} />
        ))}
      </div>
    </div>
  )
}

// Extract border radius tokens from CSS variables
const getBorderRadiusTokens = (): {
  aliasTokens: BorderRadiusToken[]
  baseTokens: BorderRadiusToken[]
} => {
  const aliasTokens: BorderRadiusToken[] = []
  const baseTokens: BorderRadiusToken[] = []

  // Alias border radius tokens (based on the CSS variables we found)
  const aliasRadiusData = [
    {
      name: "radius1",
      baseRef: "none",
      value: "0px",
      display: "Radius 1 (None)",
    },
    { name: "radius2", baseRef: "4", value: "4px", display: "Radius 2" },
    { name: "radius3", baseRef: "6", value: "6px", display: "Radius 3" },
    { name: "radius4", baseRef: "8", value: "8px", display: "Radius 4" },
    { name: "radius5", baseRef: "10", value: "10px", display: "Radius 5" },
    { name: "radius6", baseRef: "12", value: "12px", display: "Radius 6" },
    { name: "radius7", baseRef: "14", value: "14px", display: "Radius 7" },
    { name: "radius8", baseRef: "16", value: "16px", display: "Radius 8" },
    { name: "radius9", baseRef: "20", value: "20px", display: "Radius 9" },
    { name: "radius10", baseRef: "24", value: "24px", display: "Radius 10" },
    {
      name: "radius11",
      baseRef: "full",
      value: "9999px",
      display: "Radius 11 (Full)",
    },
  ]

  aliasRadiusData.forEach((item) => {
    aliasTokens.push({
      cssVariable: `--apl-alias-radius-${item.name}`,
      pixelValue: item.value,
      category: "Alias",
      displayName: item.display,
    })
  })

  // Base border radius tokens (commonly used values)
  const baseRadiusData = [
    { name: "none", value: "0px", display: "None" },
    { name: "2", value: "2px", display: "2px" },
    { name: "4", value: "4px", display: "4px" },
    { name: "6", value: "6px", display: "6px" },
    { name: "8", value: "8px", display: "8px" },
    { name: "10", value: "10px", display: "10px" },
    { name: "12", value: "12px", display: "12px" },
    { name: "14", value: "14px", display: "14px" },
    { name: "16", value: "16px", display: "16px" },
    { name: "18", value: "18px", display: "18px" },
    { name: "20", value: "20px", display: "20px" },
    { name: "22", value: "22px", display: "22px" },
    { name: "24", value: "24px", display: "24px" },
    { name: "26", value: "26px", display: "26px" },
    { name: "28", value: "28px", display: "28px" },
    { name: "30", value: "30px", display: "30px" },
    { name: "32", value: "32px", display: "32px" },
    { name: "full", value: "9999px", display: "Full (Circle)" },
  ]

  baseRadiusData.forEach((item) => {
    baseTokens.push({
      cssVariable: `--apl-base-radius-${item.name}`,
      pixelValue: item.value,
      category: "Base",
      displayName: item.display,
    })
  })

  return { aliasTokens, baseTokens }
}

export const BorderRadiusPalette: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("")
  const { aliasTokens, baseTokens } = useMemo(() => getBorderRadiusTokens(), [])

  return (
    <Theme>
      <div className={styles.borderRadiusPalette}>
        <div className={styles.searchContainer}>
          <Input
            placeholder="Search border radius tokens..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            fullWidth
          />
        </div>

        <div className={styles.borderRadiusGroups}>
          <BorderRadiusGroup
            title="Alias Tokens"
            tokens={aliasTokens}
            searchTerm={searchTerm}
          />

          <BorderRadiusGroup
            title="Base Tokens"
            tokens={baseTokens}
            searchTerm={searchTerm}
          />
        </div>
      </div>
    </Theme>
  )
}
