/* Border Radius Palette Styles */

.borderRadiusPalette {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 100%;
}

.searchContainer {
  width: 100%;
}

.borderRadiusGroups {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.borderRadiusGroup {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.borderRadiusGroupTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  border-bottom: 1px solid var(--apl-alias-color-outline-and-border-border);
  padding-bottom: 0.5rem;
}

.borderRadiusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.borderRadiusSwatch {
  display: flex;
  flex-direction: column;
  background: var(--apl-alias-color-background-and-surface-surface);
  border: 1px solid var(--apl-alias-color-outline-and-border-outline);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  text-align: left;
  position: relative;
}

.borderRadiusSwatch:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.borderRadiusSwatchPreview {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 1rem 1rem;
  background: var(--apl-alias-color-background-and-surface-background);
  position: relative;
  min-height: 120px;
  justify-content: center;
}

.borderRadiusContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.borderRadiusDemo {
  width: 64px;
  height: 64px;
  background: var(--apl-alias-color-primary-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
}

.borderRadiusContent {
  color: white;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  line-height: 1;
}

.borderRadiusMeasurement {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--apl-alias-color-background-and-surface-on-surface-variant);
  background: var(--apl-alias-color-background-and-surface-surface-variant);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}


@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(-4px); }
  20% { opacity: 1; transform: translateY(0); }
  80% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-4px); }
}

.borderRadiusSwatchInfo {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.borderRadiusSwatchVariable {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--apl-alias-color-primary-primary);
  border-radius: 4px;
  word-break: break-all;
}

.borderRadiusSwatchName {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .borderRadiusGrid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 0.75rem;
  }
  
  .borderRadiusSwatchPreview {
    padding: 1rem 0.75rem 0.75rem;
    min-height: 100px;
  }
  
  .borderRadiusDemo {
    width: 48px;
    height: 48px;
  }
  
  .borderRadiusContent {
    font-size: 14px;
  }
  
  .borderRadiusSwatchInfo {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .borderRadiusGrid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .borderRadiusGroupTitle {
    font-size: 1.25rem;
  }
  
  .borderRadiusSwatchPreview {
    padding: 0.75rem 0.5rem 0.5rem;
    min-height: 80px;
  }
  
  .borderRadiusDemo {
    width: 40px;
    height: 40px;
  }
  
  .borderRadiusContent {
    font-size: 12px;
  }
  
  .borderRadiusSwatchInfo {
    padding: 0.5rem;
  }
  
  .borderRadiusSwatchVariable {
    font-size: 0.75rem;
  }
  
  .borderRadiusSwatchName {
    font-size: 0.75rem;
  }
}
