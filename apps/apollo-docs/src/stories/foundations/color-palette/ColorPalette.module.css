
.colorControls {
  margin-bottom: 2rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.colorSections {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.colorSection {
  width: 100%;
}

.sectionHeading {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--apl-alias-color-outline-and-border-outline);
}


@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.colorSearch {
  width: 100%;
}

.toggleContainer {
  margin-top: 1rem;
}

.toggleLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  cursor: pointer;
}

.toggleInput {
  margin: 0;
}

.colorGroup {
  margin-bottom: 3rem;
}

.colorSubgroupTitle {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  position: relative;
}


.colorGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .colorGrid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .colorGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}


.colorPreview {
  height: 80px;
  width: 100%;
  border: none;
  cursor: pointer;
  position: relative;
  background: none;
  padding: 0;
}

/* Color Swatch Grid Layout Styles */
.colorSwatch {
  display: flex;
  flex-direction: column;
  background: var(--apl-alias-color-background-and-surface-surface);
  border: 1px solid var(--apl-alias-color-outline-and-border-outline);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  text-align: left;
}

.colorSwatch:hover {
  box-shadow: 0 4px 12px var(--apl-alias-color-effects-shadow);
  transform: translateY(-2px);
}

.colorSwatchPreview {
  width: 100%;
  height: 120px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--apl-alias-color-outline-and-border-outline);
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.colorSwatchInfo {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.colorSwatchVariable {
  font-size: 0.875rem;
  color: var(--apl-alias-color-primary-primary);
  font-weight: 500;
  word-break: break-all;
  line-height: 1.4;
}

.colorSwatchHex {
  font-size: 0.75rem;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  font-weight: 500;
  text-transform: uppercase;
}