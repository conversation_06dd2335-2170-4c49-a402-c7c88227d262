import React, { useMemo, useState } from "react"
import { Input, Theme } from "@apollo/ui"
import { CheckCircle } from "@design-systems/apollo-icons"

import {
  categoryTitles,
  getColorTokens,
  type ColorToken,
} from "../../utils/colorTokens"
import styles from "./ColorPalette.module.css"

interface ColorSwatchProps {
  token: ColorToken
}

const ColorSwatch: React.FC<ColorSwatchProps> = ({ token }) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error("Failed to copy text: ", err)
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault()
      handleCopy(token.cssVariable)
    }
  }

  return (
    <button
      className={styles.colorSwatch}
      onClick={() => handleCopy(token.cssVariable)}
      onKeyDown={handleKeyDown}
      aria-label={`Copy ${token.cssVariable} to clipboard`}
      title={`Click to copy ${token.cssVariable}`}
    >
      <div
        className={styles.colorSwatchPreview}
        style={{ backgroundColor: token.hexValue }}
      >
        {copied && (
          <div
            style={{
              position: "absolute",
              top: "4px",
              right: "4px",
              display: "flex",
              alignItems: "center",
              gap: "4px",
              padding: "4px 8px",
              backgroundColor: "#10b981",
              color: "white",
              borderRadius: "4px",
              fontSize: "11px",
              fontWeight: "500",
            }}
          >
            <CheckCircle size={12} />
            Copied
          </div>
        )}
      </div>
      <div className={styles.colorSwatchInfo}>
        <div className={styles.colorSwatchVariable}>{token.cssVariable}</div>
        <div className={styles.colorSwatchHex}>{token.hexValue}</div>
      </div>
    </button>
  )
}

interface ColorGroupProps {
  title: string
  colors: ColorToken[]
  searchTerm: string
}

interface ColorSubgroupProps {
  title: string
  colors: ColorToken[]
}

const ColorSubgroup: React.FC<ColorSubgroupProps> = ({ title, colors }) => {
  return (
    <div className={styles.colorSubgroup}>
      <h4 className={styles.colorSubgroupTitle}>{title}</h4>
      <div className={styles.colorGrid}>
        {colors.map((token) => (
          <ColorSwatch key={token.cssVariable} token={token} />
        ))}
      </div>
    </div>
  )
}

const ColorGroup: React.FC<ColorGroupProps> = ({
  title,
  colors,
  searchTerm,
}) => {
  const filteredAndGroupedColors = useMemo(() => {
    // First filter colors based on search term
    const filteredColors = searchTerm
      ? colors.filter(
          (color) =>
            color.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            color.cssVariable
              .toLowerCase()
              .includes(searchTerm.toLowerCase()) ||
            color.hexValue.toLowerCase().includes(searchTerm.toLowerCase())
        )
      : colors

    // Then group by subcategory
    const grouped = filteredColors.reduce(
      (acc, color) => {
        const subcategory = color.subcategory || "Other"
        if (!acc[subcategory]) {
          acc[subcategory] = []
        }
        acc[subcategory].push(color)
        return acc
      },
      {} as Record<string, ColorToken[]>
    )

    return grouped
  }, [colors, searchTerm])

  const hasColors = Object.keys(filteredAndGroupedColors).length > 0

  if (!hasColors) return null

  return (
    <div className={styles.colorGroup}>
      {Object.entries(filteredAndGroupedColors)
        .sort(([a], [b]) => sortSubcategories(a, b)) // Sort subcategories by custom order
        .map(([subcategory, subcategoryColors]) => (
          <ColorSubgroup
            key={subcategory}
            title={formatSubcategoryTitle(subcategory)}
            colors={sortColorsWithinSubcategory(subcategoryColors)}
          />
        ))}
    </div>
  )
}

// Helper function to sort subcategories by custom order
function sortSubcategories(a: string, b: string): number {
  const priorityOrder = [
    "primary",
    "secondary",
    "tertiary",
    "neutral",
    "success",
    "warning",
    "error",
    "danger", // Maps to Error in display
    "overlay",
  ]

  const aIndex = priorityOrder.indexOf(a.toLowerCase())
  const bIndex = priorityOrder.indexOf(b.toLowerCase())

  // If both are in priority list, sort by priority order
  if (aIndex !== -1 && bIndex !== -1) {
    return aIndex - bIndex
  }

  // If only one is in priority list, prioritize it
  if (aIndex !== -1) return -1
  if (bIndex !== -1) return 1

  // If neither is in priority list, sort alphabetically
  return a.localeCompare(b)
}

// Helper function to sort colors within a subcategory by numeric suffix
function sortColorsWithinSubcategory(colors: ColorToken[]): ColorToken[] {
  return [...colors].sort((a, b) => {
    // Extract numeric suffix from CSS variable
    const getNumericSuffix = (cssVariable: string): number => {
      const match = cssVariable.match(/-(\d+)$/)
      return match ? parseInt(match[1], 10) : 0
    }

    const aNum = getNumericSuffix(a.cssVariable)
    const bNum = getNumericSuffix(b.cssVariable)

    // Sort by numeric suffix first
    if (aNum !== bNum) {
      return aNum - bNum
    }

    // If no numeric suffix or same suffix, sort alphabetically
    return a.cssVariable.localeCompare(b.cssVariable)
  })
}

// Helper function to format subcategory titles
function formatSubcategoryTitle(subcategory: string): string {
  // Handle special cases for better readability
  const specialCases: Record<string, string> = {
    // Base color groupings
    primary: "Primary",
    secondary: "Secondary",
    tertiary: "Tertiary",
    neutral: "Neutral",
    success: "Success",
    warning: "Warning",
    danger: "Error",
    overlay: "Overlay",
    kid: "Kid Club",

    // Alias color groupings
    background: "Background and Surface",
    outline: "Outline and Border",
    error: "Error",
    effects: "Effects",
    information: "Information",
    state: "State",
  }

  return (
    specialCases[subcategory.toLowerCase()] ||
    subcategory
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  )
}

export const ColorPalette: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("")
  const colorTokens = useMemo(() => getColorTokens(), [])

  // Filter all tokens based on search term
  const filteredTokens = useMemo(() => {
    if (!searchTerm) {
      return colorTokens
    }

    const filtered: Record<string, ColorToken[]> = {}
    Object.entries(colorTokens).forEach(([category, tokens]) => {
      const filteredCategoryTokens = tokens.filter(
        (token) =>
          token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          token.cssVariable.toLowerCase().includes(searchTerm.toLowerCase()) ||
          token.hexValue.toLowerCase().includes(searchTerm.toLowerCase())
      )
      if (filteredCategoryTokens.length > 0) {
        filtered[category] = filteredCategoryTokens
      }
    })
    return filtered
  }, [colorTokens, searchTerm])

  return (
    <Theme>
      <div className={styles.colorControls}>
        {/* Search and Controls */}
        <div className={styles.colorSearch}>
          <Input
            placeholder="Search all colors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            fullWidth
          />
        </div>
      </div>

      {/* Color Sections */}
      <div className={styles.colorSections}>
        {/* Alias (Semantic) Colors Section - First */}
        {filteredTokens.alias && filteredTokens.alias.length > 0 && (
          <section className={styles.colorSection}>
            <h2 id="alias-color" className={styles.sectionHeading}>
              Alias Tokens
            </h2>
            <ColorGroup
              title={categoryTitles.alias}
              colors={filteredTokens.alias}
              searchTerm={searchTerm}
            />
          </section>
        )}

        {/* Base Colors Section - Second */}
        {filteredTokens.base && filteredTokens.base.length > 0 && (
          <section id="base-color" className={styles.colorSection}>
            <h2 id="base-color" className={styles.sectionHeading}>
              Base Tokens
            </h2>
            <ColorGroup
              title={categoryTitles.base}
              colors={filteredTokens.base}
              searchTerm={searchTerm}
            />
          </section>
        )}
      </div>
    </Theme>
  )
}
