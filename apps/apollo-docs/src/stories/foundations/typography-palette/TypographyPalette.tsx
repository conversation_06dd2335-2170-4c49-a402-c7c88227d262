import React, { useMemo } from "react"
import { Theme } from "@apollo/ui"
import styles from "./TypographyPalette.module.css"

type TypographyStyle = {
  name: string;
  displayName: string;
  fontFamily: string;
  fontSize: string;
  fontWeight: string;
  weightEmphasized?: string | null;
  lineHeight: string;
  cssVariables: {
    fontFamily: string;
    fontSize: string;
    fontWeight: string;
    lineHeight: string;
    weightEmphasized?: string | null;
  };
};

// Sample text for typography previews
const SAMPLE_TEXT = "The quick brown fox jumps over the lazy dog"



interface TypographyTableRowProps {
  style: TypographyStyle
}

const TypographyTableRow: React.FC<TypographyTableRowProps> = ({ style }) => {
  // Extract computed values from CSS variables
  const getComputedValue = (cssValue: string): string => {
    // Remove var() wrapper and fallback values to get clean computed values
    if (cssValue.startsWith('var(')) {
      // Extract the fallback value (after the comma)
      const match = cssValue.match(/var\([^,]+,\s*([^)]+)\)/)
      return match ? match[1].replace(/"/g, '') : cssValue
    }
    return cssValue
  }

  const computedFontFamily = getComputedValue(style.fontFamily)
  const computedFontSize = getComputedValue(style.fontSize)
  const computedFontWeight = getComputedValue(style.fontWeight)
  const computedWeightEmphasized = style.weightEmphasized ? getComputedValue(style.weightEmphasized) : null
  const computedLineHeight = getComputedValue(style.lineHeight)

  return (
    <tr className={styles.typographyTableRow}>
      <td className={styles.styleNameCell}>
        {style.displayName}
      </td>
      <td className={styles.previewCell}>
        <div
          className={styles.previewText}
          style={{
            fontFamily: style.fontFamily,
            fontSize: style.fontSize,
            fontWeight: style.fontWeight,
            lineHeight: style.lineHeight,
          }}
        >
          Apollo
        </div>
      </td>
      <td className={styles.tokensCell}>
        <div className={styles.tokenList}>
          <div className={styles.tokenItem}>
            <span className={styles.tokenLabel}>Font Family:</span>
            <span className={styles.tokenValue}>{style.cssVariables.fontFamily}</span>
          </div>
          <div className={styles.tokenItem}>
            <span className={styles.tokenLabel}>Font Size:</span>
            <span className={styles.tokenValue}>{style.cssVariables.fontSize}</span>
          </div>
          <div className={styles.tokenItem}>
            <span className={styles.tokenLabel}>Font Weight:</span>
            <span className={styles.tokenValue}>{style.cssVariables.fontWeight}</span>
          </div>
          {style.cssVariables.weightEmphasized && <div className={styles.tokenItem}>
            <span className={styles.tokenLabel}>Weight Emphasized:</span>
            <span className={styles.tokenValue}>{style.cssVariables.weightEmphasized}</span>
          </div>}
          <div className={styles.tokenItem}>
            <span className={styles.tokenLabel}>Line Height:</span>
            <span className={styles.tokenValue}>{style.cssVariables.lineHeight}</span>
          </div>
        </div>
      </td>
      <td className={styles.propertiesCell}>
        <div className={styles.propertyList}>
          <div className={styles.propertyItem}>
            <span className={styles.propertyLabel}>Font Family:</span>
            <span className={styles.propertyValue}>{computedFontFamily}</span>
          </div>
          <div className={styles.propertyItem}>
            <span className={styles.propertyLabel}>Font Size:</span>
            <span className={styles.propertyValue}>{computedFontSize}</span>
          </div>
          <div className={styles.propertyItem}>
            <span className={styles.propertyLabel}>Font Weight:</span>
            <span className={styles.propertyValue}>{computedFontWeight}</span>
          </div>
          {computedWeightEmphasized && <div className={styles.propertyItem}>
            <span className={styles.propertyLabel}>Weight Emphasized:</span>
            <span className={styles.propertyValue}>{computedWeightEmphasized}</span>
          </div>}
          <div className={styles.propertyItem}>
            <span className={styles.propertyLabel}>Line Height:</span>
            <span className={styles.propertyValue}>{computedLineHeight}</span>
          </div>
        </div>
      </td>
    </tr>
  )
}





// Function to extract typography styles (complete style definitions)
const extractTypographyStyles = (): TypographyStyle[] => {
  // Since rootStyles extraction isn't working reliably in this environment,
  // we'll return empty array and rely on the fallback styles defined in the component
  return []
}

// Base typography tokens interface
interface BaseTypographyToken {
  name: string
  value: string
  category: 'font-family' | 'font-size' | 'font-weight' | 'line-height'
}

// Function to extract base typography tokens
const extractBaseTypographyTokens = (): BaseTypographyToken[] => {
  const baseTokens: BaseTypographyToken[] = [
    // Font Family tokens
    { name: '--apl-base-typography-font-family-ibm-plex-sans-thai', value: '"IBM Plex Sans Thai"', category: 'font-family' },

    // Font Size tokens
    { name: '--apl-base-typography-font-size-4xs', value: '10px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-3xs', value: '12px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-2xs', value: '14px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-xs', value: '16px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-xs-sm', value: '18px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-xs-md', value: '20px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-xs-plus', value: '22px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-sm', value: '24px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-md', value: '28px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-lg', value: '32px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-xl', value: '36px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-2xl', value: '45px', category: 'font-size' },
    { name: '--apl-base-typography-font-size-3xl', value: '57px', category: 'font-size' },

    // Font Weight tokens
    { name: '--apl-base-typography-font-weight-weight1', value: '400', category: 'font-weight' },
    { name: '--apl-base-typography-font-weight-weight2', value: '600', category: 'font-weight' },
    { name: '--apl-base-typography-font-weight-weight3', value: '700', category: 'font-weight' },

    // Line Height tokens (key ones)
    { name: '--apl-base-typography-line-height-line-height10', value: '20px', category: 'line-height' },
    { name: '--apl-base-typography-line-height-line-height13', value: '24px', category: 'line-height' },
    { name: '--apl-base-typography-line-height-line-height17', value: '30px', category: 'line-height' },
    { name: '--apl-base-typography-line-height-line-height21', value: '36px', category: 'line-height' },
    { name: '--apl-base-typography-line-height-line-height29', value: '52px', category: 'line-height' },
    { name: '--apl-base-typography-line-height-line-height31', value: '56px', category: 'line-height' },
    { name: '--apl-base-typography-line-height-line-height44', value: '80px', category: 'line-height' },
    { name: '--apl-base-typography-line-height-line-height49', value: '88px', category: 'line-height' },
  ]

  return baseTokens
}

// Component for base typography tokens list
const BaseTypographyTokensList: React.FC = () => {
  const baseTokens = extractBaseTypographyTokens()

  // Group tokens by category
  const groupedTokens = baseTokens.reduce((acc, token) => {
    if (!acc[token.category]) {
      acc[token.category] = []
    }
    acc[token.category].push(token)
    return acc
  }, {} as Record<string, BaseTypographyToken[]>)

  const categoryDisplayNames = {
    'font-family': 'Font Family',
    'font-size': 'Font Size',
    'font-weight': 'Font Weight',
    'line-height': 'Line Height'
  }

  return (
    <div className={styles.baseTokensList}>
      {Object.entries(groupedTokens).map(([category, tokens]) => (
        <div key={category} className={styles.baseTokensCategory}>
          <h3 className={styles.baseTokensCategoryTitle}>
            {categoryDisplayNames[category as keyof typeof categoryDisplayNames]}
          </h3>
          <div className={styles.baseTokensItems}>
            {tokens.map((token) => (
              <div key={token.name} className={styles.baseTokensItem}>
                <div className={styles.baseTokensItemHeader}>
                  <code className={styles.baseTokensCode}>{token.name}</code>
                  <span className={styles.baseTokensValue}>
                    <code className={styles.baseTokensCode}>{token.value}</code>
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

export const TypographyPalette: React.FC = () => {
  // Extract typography styles
  const typographyStyles = useMemo(() => extractTypographyStyles(), [])

  // Create some hardcoded styles as fallback for testing
  const fallbackStyles: TypographyStyle[] = [
    {
      name: 'display-large',
      displayName: 'Display Large',
      fontFamily: 'var(--apl-alias-typography-display-large-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-display-large-font-size, 57px)',
      fontWeight: 'var(--apl-alias-typography-display-large-font-weight, 700)',
      lineHeight: 'var(--apl-alias-typography-display-large-line-height, 88px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-display-large-font-family',
        fontSize: '--apl-alias-typography-display-large-font-size',
        fontWeight: '--apl-alias-typography-display-large-font-weight',
        lineHeight: '--apl-alias-typography-display-large-line-height',
      }
    },
    {
      name: 'display-medium',
      displayName: 'Display Medium',
      fontFamily: 'var(--apl-alias-typography-display-medium-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-display-medium-font-size, 45px)',
      fontWeight: 'var(--apl-alias-typography-display-medium-font-weight, 700)',
      lineHeight: 'var(--apl-alias-typography-display-medium-line-height, 80px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-display-medium-font-family',
        fontSize: '--apl-alias-typography-display-medium-font-size',
        fontWeight: '--apl-alias-typography-display-medium-font-weight',
        lineHeight: '--apl-alias-typography-display-medium-line-height',
      }
    },
    {
      name: 'display-small',
      displayName: 'Display Small',
      fontFamily: 'var(--apl-alias-typography-display-small-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-display-small-font-size, 36px)',
      fontWeight: 'var(--apl-alias-typography-display-small-font-weight, 700)',
      lineHeight: 'var(--apl-alias-typography-display-small-line-height, 56px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-display-small-font-family',
        fontSize: '--apl-alias-typography-display-small-font-size',
        fontWeight: '--apl-alias-typography-display-small-font-weight',
        lineHeight: '--apl-alias-typography-display-small-line-height',
      }
    },
    {
      name: 'headline-large',
      displayName: 'Headline Large',
      fontFamily: 'var(--apl-alias-typography-headline-large-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-headline-large-font-size, 32px)',
      fontWeight: 'var(--apl-alias-typography-headline-large-font-weight, 700)',
      lineHeight: 'var(--apl-alias-typography-headline-large-line-height, 52px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-headline-large-font-family',
        fontSize: '--apl-alias-typography-headline-large-font-size',
        fontWeight: '--apl-alias-typography-headline-large-font-weight',
        lineHeight: '--apl-alias-typography-headline-large-line-height',
      }
    },
    {
      name: 'headline-medium',
      displayName: 'Headline Medium',
      fontFamily: 'var(--apl-alias-typography-headline-medium-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-headline-medium-font-size, 28px)',
      fontWeight: 'var(--apl-alias-typography-headline-medium-font-weight, 700)',
      lineHeight: 'var(--apl-alias-typography-headline-medium-line-height, 44px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-headline-medium-font-family',
        fontSize: '--apl-alias-typography-headline-medium-font-size',
        fontWeight: '--apl-alias-typography-headline-medium-font-weight',
        lineHeight: '--apl-alias-typography-headline-medium-line-height',
      }
    },
    {
      name: 'headline-small',
      displayName: 'Headline Small',
      fontFamily: 'var(--apl-alias-typography-headline-small-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-headline-small-font-size, 24px)',
      fontWeight: 'var(--apl-alias-typography-headline-small-font-weight, 700)',
      lineHeight: 'var(--apl-alias-typography-headline-small-line-height, 36px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-headline-small-font-family',
        fontSize: '--apl-alias-typography-headline-small-font-size',
        fontWeight: '--apl-alias-typography-headline-small-font-weight',
        lineHeight: '--apl-alias-typography-headline-small-line-height',
      }
    },
    {
      name: 'title-large',
      displayName: 'Title Large',
      fontFamily: 'var(--apl-alias-typography-title-large-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-title-large-font-size, 22px)',
      fontWeight: 'var(--apl-alias-typography-title-large-font-weight, 600)',
      lineHeight: 'var(--apl-alias-typography-title-large-line-height, 36px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-title-large-font-family',
        fontSize: '--apl-alias-typography-title-large-font-size',
        fontWeight: '--apl-alias-typography-title-large-font-weight',
        lineHeight: '--apl-alias-typography-title-large-line-height',
      }
    },
    {
      name: 'title-medium',
      displayName: 'Title Medium',
      fontFamily: 'var(--apl-alias-typography-title-medium-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-title-medium-font-size, 20px)',
      fontWeight: 'var(--apl-alias-typography-title-medium-font-weight, 600)',
      lineHeight: 'var(--apl-alias-typography-title-medium-line-height, 36px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-title-medium-font-family',
        fontSize: '--apl-alias-typography-title-medium-font-size',
        fontWeight: '--apl-alias-typography-title-medium-font-weight',
        lineHeight: '--apl-alias-typography-title-medium-line-height',
      }
    },
    {
      name: 'title-small',
      displayName: 'Title Small',
      fontFamily: 'var(--apl-alias-typography-title-small-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-title-small-font-size, 18px)',
      fontWeight: 'var(--apl-alias-typography-title-small-font-weight, 700)',
      lineHeight: 'var(--apl-alias-typography-title-small-line-height, 30px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-title-small-font-family',
        fontSize: '--apl-alias-typography-title-small-font-size',
        fontWeight: '--apl-alias-typography-title-small-font-weight',
        lineHeight: '--apl-alias-typography-title-small-line-height',
      }
    },
    {
      name: 'body-large',
      displayName: 'Body Large',
      fontFamily: 'var(--apl-alias-typography-body-large-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-body-large-font-size, 16px)',
      fontWeight: 'var(--apl-alias-typography-body-large-font-weight, 400)',
      weightEmphasized: 'var(--apl-alias-typography-body-large-weight-emphasized, 500)',
      lineHeight: 'var(--apl-alias-typography-body-large-line-height, 24px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-body-large-font-family',
        fontSize: '--apl-alias-typography-body-large-font-size',
        fontWeight: '--apl-alias-typography-body-large-font-weight',
        weightEmphasized: '--apl-alias-typography-body-large-weight-emphasized',
        lineHeight: '--apl-alias-typography-body-large-line-height',
      }
    },
    {
      name: 'body-medium',
      displayName: 'Body Medium',
      fontFamily: 'var(--apl-alias-typography-body-medium-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-body-medium-font-size, 14px)',
      fontWeight: 'var(--apl-alias-typography-body-medium-font-weight, 400)',
      weightEmphasized: 'var(--apl-alias-typography-body-medium-weight-emphasized, 500)',
      lineHeight: 'var(--apl-alias-typography-body-medium-line-height, 24px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-body-medium-font-family',
        fontSize: '--apl-alias-typography-body-medium-font-size',
        fontWeight: '--apl-alias-typography-body-medium-font-weight',
        weightEmphasized: '--apl-alias-typography-body-medium-weight-emphasized',
        lineHeight: '--apl-alias-typography-body-medium-line-height',
      }
    },
    {
      name: 'body-small',
      displayName: 'Body Small',
      fontFamily: 'var(--apl-alias-typography-body-small-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-body-small-font-size, 12px)',
      fontWeight: 'var(--apl-alias-typography-body-small-font-weight, 400)',
      weightEmphasized: 'var(--apl-alias-typography-body-small-weight-emphasized, 500)',
      lineHeight: 'var(--apl-alias-typography-body-small-line-height, 20px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-body-small-font-family',
        fontSize: '--apl-alias-typography-body-small-font-size',
        fontWeight: '--apl-alias-typography-body-small-font-weight',
        weightEmphasized: '--apl-alias-typography-body-small-weight-emphasized',
        lineHeight: '--apl-alias-typography-body-small-line-height',
      }
    },
    {
      name: 'label-large',
      displayName: 'Label Large',
      fontFamily: 'var(--apl-alias-typography-label-large-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-label-large-font-size, 14px)',
      fontWeight: 'var(--apl-alias-typography-label-large-font-weight, 400)',
      weightEmphasized: 'var(--apl-alias-typography-label-large-weight-emphasized, 500)',
      lineHeight: 'var(--apl-alias-typography-label-large-line-height, 20px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-label-large-font-family',
        fontSize: '--apl-alias-typography-label-large-font-size',
        fontWeight: '--apl-alias-typography-label-large-font-weight',
        weightEmphasized: '--apl-alias-typography-label-large-weight-emphasized',
        lineHeight: '--apl-alias-typography-label-large-line-height',
      }
    },
    {
      name: 'label-medium',
      displayName: 'Label Medium',
      fontFamily: 'var(--apl-alias-typography-label-medium-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-label-medium-font-size, 12px)',
      fontWeight: 'var(--apl-alias-typography-label-medium-font-weight, 400)',
      weightEmphasized: 'var(--apl-alias-typography-label-medium-weight-emphasized, 500)',
      lineHeight: 'var(--apl-alias-typography-label-medium-line-height, 20px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-label-medium-font-family',
        fontSize: '--apl-alias-typography-label-medium-font-size',
        fontWeight: '--apl-alias-typography-label-medium-font-weight',
        weightEmphasized: '--apl-alias-typography-label-medium-weight-emphasized',
        lineHeight: '--apl-alias-typography-label-medium-line-height',
      }
    },
    {
      name: 'label-small',
      displayName: 'Label Small',
      fontFamily: 'var(--apl-alias-typography-label-small-font-family, "IBM Plex Sans Thai")',
      fontSize: 'var(--apl-alias-typography-label-small-font-size, 10px)',
      fontWeight: 'var(--apl-alias-typography-label-small-font-weight, 400)',
      weightEmphasized: 'var(--apl-alias-typography-label-small-weight-emphasized, 500)',
      lineHeight: 'var(--apl-alias-typography-label-small-line-height, 24px)',
      cssVariables: {
        fontFamily: '--apl-alias-typography-label-small-font-family',
        fontSize: '--apl-alias-typography-label-small-font-size',
        fontWeight: '--apl-alias-typography-label-small-font-weight',
        weightEmphasized: '--apl-alias-typography-label-small-weight-emphasized',
        lineHeight: '--apl-alias-typography-label-small-line-height',
      }
    }
  ]

  const stylesToDisplay = typographyStyles.length > 0 ? typographyStyles : fallbackStyles

  return (
    <Theme>
      <div className={styles.typographySections}>
        <section className={styles.typographySection}>
          <h2 id="alias-typography" className={styles.baseTokensTitle}>Alias tokens with type styles</h2>
          <div className={styles.typographyTableContainer}>
            <table className={styles.typographyTable}>
              <thead>
                <tr className={styles.typographyTableHeader}>
                  <th className={styles.headerCell}>Name</th>
                  <th className={styles.headerCell}>Preview</th>
                  <th className={styles.headerCell}>Tokens</th>
                  <th className={styles.headerCell}>Default Values</th>
                </tr>
              </thead>
              <tbody>
                {stylesToDisplay.map((style) => (
                  <TypographyTableRow key={style.name} style={style} />
                ))}
              </tbody>
            </table>
          </div>
        </section>

        <section className={styles.typographySection}>
          <h2 id="base-typography" className={styles.baseTokensTitle}>Base Tokens</h2>
          <BaseTypographyTokensList />
        </section>
      </div>
    </Theme>
  )
}