import { Meta } from "@storybook/addon-docs/blocks"
import { TypographyPalette } from "./typography-palette"

<Meta title="Foundations/Typography" tags={["docs"]} />

# Typography

The Apollo design system provides a comprehensive typography system that supports consistent text styling across all components. All typography styles are defined as CSS custom properties (variables) and are organized into logical groups for easy use and maintenance.

## Fonts

We are using `IBM Plex Sans Thai` as our primary font. We don't import the font within our CSS in order to give full control of the fonts which you wish to bring to your client, the following code snippet is what we recommend in order to include our recommended fonts.

```html
<link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Thai:wght@400;500;700&display=swap" rel="stylesheet">
```

More ever this is how we are building our css variable for our main font family

```css
:root {
  --apl-base-typography-font-family-ibm-plex-sans-thai: "IBM Plex Sans Thai", sans-serif;
}
```

The typography system is organized into two main collections of variables in Figma [💙 Apollo Alias Foundations and Styles](https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=13-3&t=UZXeBdRDW7D0SWTy-1):

- **Alias**: Semantic typography mappings organized by usage roles (Display, Headline, Body, Label, etc.), use reference typography from `Base`.
- **Base**: Foundational typography primitives organized by properties (Font Family, Font Size, Font Weight, Line Height)

<TypographyPalette />

## Usage

### Recommended: Use Alias Typography

For most use cases, use alias typography tokens as they provide semantic meaning and consistent styling:

```css
.label {
  font-family: var(--apl-alias-typography-label-large-font-family);
  font-size: var(--apl-alias-typography-label-large-font-size);
  font-weight: var(--apl-alias-typography-label-large-font-weight);
  line-height: var(--apl-alias-typography-label-large-line-height);
}
```
