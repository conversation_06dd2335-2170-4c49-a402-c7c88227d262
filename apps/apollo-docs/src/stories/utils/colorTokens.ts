export interface ColorToken {
  name: string
  cssVariable: string
  hexValue: string
  category: string
  subcategory: string
  description?: string
}

// CSS variable definitions from the actual file
const CSS_VARIABLES = `
  --apl-alias-color-background-and-surface-background: light-dark(var(--apl-base-color-neutral-100), var(--apl-base-color-neutral-0));
  --apl-alias-color-background-and-surface-container-disabled: light-dark(var(--apl-base-color-neutral-95), var(--apl-base-color-neutral-10));
  --apl-alias-color-background-and-surface-on-background: light-dark(var(--apl-base-color-neutral-99), var(--apl-base-color-neutral-20));
  --apl-alias-color-background-and-surface-on-surface-variant: light-dark(var(--apl-base-color-neutral-80), var(--apl-base-color-neutral-20));
  --apl-alias-color-background-and-surface-on-surface: light-dark(var(--apl-base-color-neutral-30), var(--apl-base-color-neutral-99));
  --apl-alias-color-background-and-surface-surface-variant: light-dark(var(--apl-base-color-neutral-100), var(--apl-base-color-neutral-70));
  --apl-alias-color-background-and-surface-surface: light-dark(var(--apl-base-color-neutral-99), var(--apl-base-color-neutral-10));
  --apl-alias-color-background-and-surface-text-icon-disabled: light-dark(var(--apl-base-color-neutral-70), var(--apl-base-color-neutral-20));
  --apl-alias-color-effects-overlay-surface-black: light-dark(var(--apl-base-color-overlay-black-20), var(--apl-base-color-overlay-black-20));
  --apl-alias-color-effects-overlay-surface-disabled: light-dark(var(--apl-base-color-overlay-white-70), var(--apl-base-color-overlay-black-70));
  --apl-alias-color-effects-overlay-surface-white: light-dark(var(--apl-base-color-overlay-white-20), var(--apl-base-color-overlay-white-20));
  --apl-alias-color-effects-scrim: light-dark(var(--apl-base-color-overlay-black-40), var(--apl-base-color-overlay-black-60));
  --apl-alias-color-effects-shadow: light-dark(var(--apl-base-color-overlay-black-40), var(--apl-base-color-overlay-black-60));
  --apl-alias-color-error-container-disabled: light-dark(var(--apl-base-color-danger-99), var(--apl-base-color-danger-10));
  --apl-alias-color-error-disable: light-dark(var(--apl-base-color-danger-90), var(--apl-base-color-danger-10));
  --apl-alias-color-error-error-container: light-dark(var(--apl-base-color-danger-99), var(--apl-base-color-danger-70));
  --apl-alias-color-error-error: light-dark(var(--apl-base-color-danger-40), var(--apl-base-color-danger-90));
  --apl-alias-color-error-focused: light-dark(var(--apl-base-color-danger-60), var(--apl-base-color-danger-90));
  --apl-alias-color-error-hovered: light-dark(var(--apl-base-color-danger-50), var(--apl-base-color-danger-70));
  --apl-alias-color-error-on-error-container: light-dark(var(--apl-base-color-danger-30), var(--apl-base-color-danger-95));
  --apl-alias-color-error-on-error: light-dark(var(--apl-base-color-danger-100), var(--apl-base-color-danger-20));
  --apl-alias-color-error-pressed: light-dark(var(--apl-base-color-danger-30), var(--apl-base-color-danger-30));
  --apl-alias-color-error-text-icon-disabled: light-dark(var(--apl-base-color-danger-40), var(--apl-base-color-danger-30));
  --apl-alias-color-error-text-only-background-hovered: light-dark(var(--apl-base-color-neutral-90), var(--apl-base-color-overlay-black-20));
  --apl-alias-color-outline-and-border-border-disabled: light-dark(var(--apl-base-color-neutral-70), var(--apl-base-color-neutral-30));
  --apl-alias-color-outline-and-border-border: light-dark(var(--apl-base-color-neutral-30), var(--apl-base-color-neutral-95));
  --apl-alias-color-outline-and-border-outline-variant: light-dark(var(--apl-base-color-neutral-80), var(--apl-base-color-neutral-80));
  --apl-alias-color-outline-and-border-outline: light-dark(var(--apl-base-color-neutral-70), var(--apl-base-color-neutral-30));
  --apl-alias-color-primary-focused: light-dark(var(--apl-base-color-primary-60), var(--apl-base-color-primary-90));
  --apl-alias-color-primary-hovered: light-dark(var(--apl-base-color-primary-50), var(--apl-base-color-primary-70));
  --apl-alias-color-primary-on-primary: light-dark(var(--apl-base-color-primary-100), var(--apl-base-color-primary-20));
  --apl-alias-color-primary-pressed: light-dark(var(--apl-base-color-primary-30), var(--apl-base-color-primary-30));
  --apl-alias-color-primary-primary-container: light-dark(var(--apl-base-color-primary-95), var(--apl-base-color-primary-30));
  --apl-alias-color-primary-primary: light-dark(var(--apl-base-color-primary-40), var(--apl-base-color-primary-50));
  --apl-alias-color-primary-surface-tint: light-dark(var(--apl-base-color-primary-40), var(--apl-base-color-primary-70));
  --apl-alias-color-primary-text-only-background-hovered: light-dark(var(--apl-base-color-neutral-90), var(--apl-base-color-overlay-black-20));
  --apl-alias-color-secondary-on-secondary-container: light-dark(var(--apl-base-color-secondary-30), var(--apl-base-color-secondary-95));
  --apl-alias-color-secondary-on-secondary: light-dark(var(--apl-base-color-secondary-100), var(--apl-base-color-secondary-20));
  --apl-alias-color-secondary-secondary-container: light-dark(var(--apl-base-color-secondary-95), var(--apl-base-color-secondary-30));
  --apl-alias-color-secondary-secondary: light-dark(var(--apl-base-color-secondary-40), var(--apl-base-color-secondary-70));
  --apl-alias-color-static-text-icon-text-icon-dark: light-dark(var(--apl-base-color-neutral-10), var(--apl-base-color-neutral-10));
  --apl-alias-color-static-text-icon-text-icon-light: light-dark(var(--apl-base-color-neutral-100), var(--apl-base-color-neutral-100));
  --apl-alias-color-success-on-success-container: light-dark(var(--apl-base-color-success-30), var(--apl-base-color-success-95));
  --apl-alias-color-success-on-success: light-dark(var(--apl-base-color-success-100), var(--apl-base-color-success-20));
  --apl-alias-color-success-success-container: light-dark(var(--apl-base-color-success-99), var(--apl-base-color-success-30));
  --apl-alias-color-success-success: light-dark(var(--apl-base-color-success-50), var(--apl-base-color-primary-90));
  --apl-alias-color-tertiary-on-tertiary-container: light-dark(var(--apl-base-color-tertiary-30), var(--apl-base-color-tertiary-95));
  --apl-alias-color-tertiary-on-tertiary: light-dark(var(--apl-base-color-tertiary-100), var(--apl-base-color-tertiary-20));
  --apl-alias-color-tertiary-tertiary-container: light-dark(var(--apl-base-color-tertiary-95), var(--apl-base-color-tertiary-30));
  --apl-alias-color-tertiary-tertiary: light-dark(var(--apl-base-color-tertiary-40), var(--apl-base-color-tertiary-70));
  --apl-alias-color-warning-on-warning-container: light-dark(var(--apl-base-color-warning-30), var(--apl-base-color-warning-99));
  --apl-alias-color-warning-on-warning: light-dark(var(--apl-base-color-warning-100), var(--apl-base-color-warning-20));
  --apl-alias-color-warning-warning-container: light-dark(var(--apl-base-color-warning-99), var(--apl-base-color-warning-30));
  --apl-alias-color-warning-warning: light-dark(var(--apl-base-color-warning-50), var(--apl-base-color-warning-90));
  --apl-base-color-danger-0: var(--apl-color-red-cherry-0);
  --apl-base-color-danger-10: var(--apl-color-red-cherry-10);
  --apl-base-color-danger-100: var(--apl-color-red-cherry-100);
  --apl-base-color-danger-20: var(--apl-color-red-cherry-20);
  --apl-base-color-danger-30: var(--apl-color-red-cherry-30);
  --apl-base-color-danger-40: var(--apl-color-red-cherry-40);
  --apl-base-color-danger-50: var(--apl-color-red-cherry-50);
  --apl-base-color-danger-60: var(--apl-color-red-cherry-60);
  --apl-base-color-danger-70: var(--apl-color-red-cherry-70);
  --apl-base-color-danger-80: var(--apl-color-red-cherry-80);
  --apl-base-color-danger-90: var(--apl-color-red-cherry-90);
  --apl-base-color-danger-95: var(--apl-color-red-cherry-95);
  --apl-base-color-danger-99: var(--apl-color-red-cherry-99);
  --apl-base-color-neutral-0: var(--apl-color-gray-smoke-0);
  --apl-base-color-neutral-10: var(--apl-color-gray-smoke-10);
  --apl-base-color-neutral-100: var(--apl-color-gray-smoke-100);
  --apl-base-color-neutral-20: var(--apl-color-gray-smoke-20);
  --apl-base-color-neutral-30: var(--apl-color-gray-smoke-30);
  --apl-base-color-neutral-40: var(--apl-color-gray-smoke-40);
  --apl-base-color-neutral-50: var(--apl-color-gray-smoke-50);
  --apl-base-color-neutral-60: var(--apl-color-gray-smoke-60);
  --apl-base-color-neutral-70: var(--apl-color-gray-smoke-70);
  --apl-base-color-neutral-80: var(--apl-color-gray-smoke-80);
  --apl-base-color-neutral-90: var(--apl-color-gray-smoke-90);
  --apl-base-color-neutral-95: var(--apl-color-gray-smoke-95);
  --apl-base-color-neutral-99: var(--apl-color-gray-smoke-99);
  --apl-base-color-overlay-black-0: var(--apl-color-black-soft-0);
  --apl-base-color-overlay-black-10: var(--apl-color-black-soft-10);
  --apl-base-color-overlay-black-100: var(--apl-color-black-soft-100);
  --apl-base-color-overlay-black-20: var(--apl-color-black-soft-20);
  --apl-base-color-overlay-black-30: var(--apl-color-black-soft-30);
  --apl-base-color-overlay-black-40: var(--apl-color-black-soft-40);
  --apl-base-color-overlay-black-50: var(--apl-color-black-soft-50);
  --apl-base-color-overlay-black-60: var(--apl-color-black-soft-60);
  --apl-base-color-overlay-black-70: var(--apl-color-black-soft-70);
  --apl-base-color-overlay-black-80: var(--apl-color-black-soft-80);
  --apl-base-color-overlay-black-90: var(--apl-color-black-soft-90);
  --apl-base-color-overlay-white-0: var(--apl-color-white-soft-0);
  --apl-base-color-overlay-white-10: var(--apl-color-white-soft-10);
  --apl-base-color-overlay-white-100: var(--apl-color-white-soft-100);
  --apl-base-color-overlay-white-20: var(--apl-color-white-soft-20);
  --apl-base-color-overlay-white-30: var(--apl-color-white-soft-30);
  --apl-base-color-overlay-white-40: var(--apl-color-white-soft-40);
  --apl-base-color-overlay-white-50: var(--apl-color-white-soft-50);
  --apl-base-color-overlay-white-60: var(--apl-color-white-soft-60);
  --apl-base-color-overlay-white-70: var(--apl-color-white-soft-70);
  --apl-base-color-overlay-white-80: var(--apl-color-white-soft-80);
  --apl-base-color-overlay-white-90: var(--apl-color-white-soft-90);
  --apl-base-color-primary-0: var(--apl-color-green-pine-0);
  --apl-base-color-primary-10: var(--apl-color-green-pine-10);
  --apl-base-color-primary-100: var(--apl-color-green-pine-100);
  --apl-base-color-primary-20: var(--apl-color-green-pine-20);
  --apl-base-color-primary-30: var(--apl-color-green-pine-30);
  --apl-base-color-primary-40: var(--apl-color-green-pine-40);
  --apl-base-color-primary-50: var(--apl-color-green-pine-50);
  --apl-base-color-primary-60: var(--apl-color-green-pine-60);
  --apl-base-color-primary-70: var(--apl-color-green-pine-70);
  --apl-base-color-primary-80: var(--apl-color-green-pine-80);
  --apl-base-color-primary-90: var(--apl-color-green-pine-90);
  --apl-base-color-primary-95: var(--apl-color-green-pine-95);
  --apl-base-color-primary-99: var(--apl-color-green-pine-99);
  --apl-base-color-secondary-0: var(--apl-color-gray-bluish-0);
  --apl-base-color-secondary-10: var(--apl-color-gray-bluish-10);
  --apl-base-color-secondary-100: var(--apl-color-gray-bluish-100);
  --apl-base-color-secondary-20: var(--apl-color-gray-bluish-20);
  --apl-base-color-secondary-30: var(--apl-color-gray-bluish-30);
  --apl-base-color-secondary-40: var(--apl-color-gray-bluish-40);
  --apl-base-color-secondary-50: var(--apl-color-gray-bluish-50);
  --apl-base-color-secondary-60: var(--apl-color-gray-bluish-60);
  --apl-base-color-secondary-70: var(--apl-color-gray-bluish-70);
  --apl-base-color-secondary-80: var(--apl-color-gray-bluish-80);
  --apl-base-color-secondary-90: var(--apl-color-gray-bluish-90);
  --apl-base-color-secondary-95: var(--apl-color-gray-bluish-95);
  --apl-base-color-secondary-99: var(--apl-color-gray-bluish-99);
  --apl-base-color-success-0: var(--apl-color-green-matcha-0);
  --apl-base-color-success-10: var(--apl-color-green-matcha-10);
  --apl-base-color-success-100: var(--apl-color-green-matcha-100);
  --apl-base-color-success-20: var(--apl-color-green-matcha-20);
  --apl-base-color-success-30: var(--apl-color-green-matcha-30);
  --apl-base-color-success-40: var(--apl-color-green-matcha-40);
  --apl-base-color-success-50: var(--apl-color-green-matcha-50);
  --apl-base-color-success-60: var(--apl-color-green-matcha-60);
  --apl-base-color-success-70: var(--apl-color-green-matcha-70);
  --apl-base-color-success-80: var(--apl-color-green-matcha-80);
  --apl-base-color-success-90: var(--apl-color-green-matcha-90);
  --apl-base-color-success-95: var(--apl-color-green-matcha-95);
  --apl-base-color-success-99: var(--apl-color-green-matcha-99);
  --apl-base-color-tertiary-0: var(--apl-color-blue-ocean-0);
  --apl-base-color-tertiary-10: var(--apl-color-blue-ocean-10);
  --apl-base-color-tertiary-100: var(--apl-color-blue-ocean-100);
  --apl-base-color-tertiary-20: var(--apl-color-blue-ocean-20);
  --apl-base-color-tertiary-30: var(--apl-color-blue-ocean-30);
  --apl-base-color-tertiary-40: var(--apl-color-blue-ocean-40);
  --apl-base-color-tertiary-50: var(--apl-color-blue-ocean-50);
  --apl-base-color-tertiary-60: var(--apl-color-blue-ocean-60);
  --apl-base-color-tertiary-70: var(--apl-color-blue-ocean-70);
  --apl-base-color-tertiary-80: var(--apl-color-blue-ocean-80);
  --apl-base-color-tertiary-90: var(--apl-color-blue-ocean-90);
  --apl-base-color-tertiary-95: var(--apl-color-blue-ocean-95);
  --apl-base-color-tertiary-99: var(--apl-color-blue-ocean-99);
  --apl-base-color-warning-0: var(--apl-color-yellow-peanut-0);
  --apl-base-color-warning-10: var(--apl-color-yellow-peanut-10);
  --apl-base-color-warning-100: var(--apl-color-yellow-peanut-100);
  --apl-base-color-warning-20: var(--apl-color-yellow-peanut-20);
  --apl-base-color-warning-30: var(--apl-color-yellow-peanut-30);
  --apl-base-color-warning-40: var(--apl-color-yellow-peanut-40);
  --apl-base-color-warning-50: var(--apl-color-yellow-peanut-50);
  --apl-base-color-warning-60: var(--apl-color-yellow-peanut-60);
  --apl-base-color-warning-70: var(--apl-color-yellow-peanut-70);
  --apl-base-color-warning-80: var(--apl-color-yellow-peanut-80);
  --apl-base-color-warning-90: var(--apl-color-yellow-peanut-90);
  --apl-base-color-warning-95: var(--apl-color-yellow-peanut-95);
  --apl-base-color-warning-99: var(--apl-color-yellow-peanut-99);
  --apl-color-black-soft-0: #0000000D;
  --apl-color-black-soft-10: #0000001A;
  --apl-color-black-soft-100: #000000;
  --apl-color-black-soft-20: #00000033;
  --apl-color-black-soft-30: #0000004D;
  --apl-color-black-soft-40: #00000066;
  --apl-color-black-soft-50: #00000080;
  --apl-color-black-soft-60: #00000099;
  --apl-color-black-soft-70: #000000B3;
  --apl-color-black-soft-80: #000000CC;
  --apl-color-black-soft-90: #000000E6;
  --apl-color-blue-ocean-0: #000000;
  --apl-color-blue-ocean-10: #001159;
  --apl-color-blue-ocean-100: #FFFFFF;
  --apl-color-blue-ocean-20: #00218C;
  --apl-color-blue-ocean-30: #0032C4;
  --apl-color-blue-ocean-40: #2E4EDC;
  --apl-color-blue-ocean-50: #4C6AF6;
  --apl-color-blue-ocean-60: #7087FF;
  --apl-color-blue-ocean-70: #95A6FF;
  --apl-color-blue-ocean-80: #B9C3FF;
  --apl-color-blue-ocean-90: #DEE1FF;
  --apl-color-blue-ocean-95: #F0EFFF;
  --apl-color-blue-ocean-99: #FEFBFF;
  --apl-color-gray-bluish-0: #000000;
  --apl-color-gray-bluish-10: #131C2B;
  --apl-color-gray-bluish-100: #FFFFFF;
  --apl-color-gray-bluish-20: #283141;
  --apl-color-gray-bluish-30: #3E4758;
  --apl-color-gray-bluish-40: #565F71;
  --apl-color-gray-bluish-50: #6E778A;
  --apl-color-gray-bluish-60: #8891A4;
  --apl-color-gray-bluish-70: #A2ABC0;
  --apl-color-gray-bluish-80: #BEC7DB;
  --apl-color-gray-bluish-90: #DAE3F8;
  --apl-color-gray-bluish-95: #ECF0FF;
  --apl-color-gray-bluish-99: #FDFBFF;
  --apl-color-gray-smoke-0: #000000;
  --apl-color-gray-smoke-10: #1C1B1C;
  --apl-color-gray-smoke-100: #FFFFFF;
  --apl-color-gray-smoke-20: #313031;
  --apl-color-gray-smoke-30: #474647;
  --apl-color-gray-smoke-40: #5F5E5F;
  --apl-color-gray-smoke-50: #787777;
  --apl-color-gray-smoke-60: #929091;
  --apl-color-gray-smoke-70: #ADABAB;
  --apl-color-gray-smoke-80: #C8C6C6;
  --apl-color-gray-smoke-90: #E5E2E2;
  --apl-color-gray-smoke-95: #F3F0F0;
  --apl-color-gray-smoke-99: #F8F7F7;
  --apl-color-green-matcha-0: #000000;
  --apl-color-green-matcha-10: #233D18;
  --apl-color-green-matcha-100: #FFFFFF;
  --apl-color-green-matcha-20: #2F5220;
  --apl-color-green-matcha-30: #477A2F;
  --apl-color-green-matcha-40: #5EA33F;
  --apl-color-green-matcha-50: #76CC4F;
  --apl-color-green-matcha-60: #91D672;
  --apl-color-green-matcha-70: #ADE095;
  --apl-color-green-matcha-80: #C8EBB9;
  --apl-color-green-matcha-90: #D6F0CA;
  --apl-color-green-matcha-95: #E4F5DC;
  --apl-color-green-matcha-99: #F1FAED;
  --apl-color-green-pine-0: #000000;
  --apl-color-green-pine-10: #002109;
  --apl-color-green-pine-100: #FFFFFF;
  --apl-color-green-pine-20: #003915;
  --apl-color-green-pine-30: #005321;
  --apl-color-green-pine-40: #016E2E;
  --apl-color-green-pine-50: #2C8745;
  --apl-color-green-pine-60: #49A25C;
  --apl-color-green-pine-70: #64BE74;
  --apl-color-green-pine-80: #7FDA8E;
  --apl-color-green-pine-90: #9BF7A7;
  --apl-color-green-pine-95: #C5FFC8;
  --apl-color-green-pine-99: #F6FFF2;
  --apl-color-lilac-soft-0: #000000;
  --apl-color-lilac-soft-10: #3F2745;
  --apl-color-lilac-soft-100: #FFFFFF;
  --apl-color-lilac-soft-20: #5E4264;
  --apl-color-lilac-soft-30: #8C6C94;
  --apl-color-lilac-soft-40: #AB87B3;
  --apl-color-lilac-soft-50: #CAA3D3;
  --apl-color-lilac-soft-60: #D5B5DC;
  --apl-color-lilac-soft-70: #DFC8E5;
  --apl-color-lilac-soft-80: #EADAED;
  --apl-color-lilac-soft-90: #EFE3F2;
  --apl-color-lilac-soft-95: #F4EDF6;
  --apl-color-lilac-soft-99: #FAF6FB;
  --apl-color-red-cherry-0: #000000;
  --apl-color-red-cherry-10: #410001;
  --apl-color-red-cherry-100: #FFFFFF;
  --apl-color-red-cherry-20: #690003;
  --apl-color-red-cherry-30: #930006;
  --apl-color-red-cherry-40: #C0000B;
  --apl-color-red-cherry-50: #E9211E;
  --apl-color-red-cherry-60: #FF5546;
  --apl-color-red-cherry-70: #FF8A7B;
  --apl-color-red-cherry-80: #FFB4AA;
  --apl-color-red-cherry-90: #FFDAD5;
  --apl-color-red-cherry-95: #FFEDEA;
  --apl-color-red-cherry-99: #FFF4F3;
  --apl-color-white-soft-0: #FFFFFF0D;
  --apl-color-white-soft-10: #FFFFFF1A;
  --apl-color-white-soft-100: #FFFFFF;
  --apl-color-white-soft-20: #FFFFFF33;
  --apl-color-white-soft-30: #FFFFFF4D;
  --apl-color-white-soft-40: #FFFFFF66;
  --apl-color-white-soft-50: #FFFFFF80;
  --apl-color-white-soft-60: #FFFFFF99;
  --apl-color-white-soft-70: #FFFFFFB3;
  --apl-color-white-soft-80: #FFFFFFCC;
  --apl-color-white-soft-90: #FFFFFFE6;
  --apl-color-yellow-peanut-0: #000000;
  --apl-color-yellow-peanut-10: #403300;
  --apl-color-yellow-peanut-100: #FFFFFF;
  --apl-color-yellow-peanut-20: #665200;
  --apl-color-yellow-peanut-30: #8C7000;
  --apl-color-yellow-peanut-40: #B38F00;
  --apl-color-yellow-peanut-50: #D9AF09;
  --apl-color-yellow-peanut-60: #FFD118;
  --apl-color-yellow-peanut-70: #FFD940;
  --apl-color-yellow-peanut-80: #FFE169;
  --apl-color-yellow-peanut-90: #FFE991;
  --apl-color-yellow-peanut-95: #FFF1BA;
  --apl-color-yellow-peanut-99: #FFFAE6;
`;

// Helper function to resolve base color references to actual hex values
function resolveColorValue(value: string): string {
  // Handle light-dark() function - extract the light value for display
  const lightDarkMatch = value.match(/light-dark\(([^,]+),/);
  if (lightDarkMatch) {
    value = lightDarkMatch[1].trim();
  }

  // Handle var() references
  const varMatch = value.match(/var\(([^)]+)\)/);
  if (varMatch) {
    const varName = varMatch[1].trim();
    // Look up the variable in our CSS_VARIABLES string
    const regex = new RegExp(`${varName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}:\\s*([^;]+)`);
    const match = CSS_VARIABLES.match(regex);
    if (match) {
      return resolveColorValue(match[1].trim());
    }
  }

  // Return hex value directly, removing any trailing semicolon
  return value.replace(/;$/, '');
}

// Helper function to format display names
function formatDisplayName(cssVariable: string, isAlias: boolean): string {
  if (isAlias) {
    const parts = cssVariable.replace('--apl-alias-color-', '').split('-');
    return parts.map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  } else {
    const parts = cssVariable.replace('--apl-base-color-', '').split('-');
    const category = parts[0];
    const shade = parts[parts.length - 1];
    return `${category.charAt(0).toUpperCase() + category.slice(1)} ${shade}`;
  }
}

// Helper function to get subcategory for organization
function getSubcategory(cssVariable: string, isAlias: boolean): string {
  if (isAlias) {
    const parts = cssVariable.replace('--apl-alias-color-', '').split('-');
    return parts[0]; // First part is the main category (primary, error, etc.)
  } else {
    const parts = cssVariable.replace('--apl-base-color-', '').split('-');
    return parts[0]; // First part is the color family (primary, neutral, etc.)
  }
}

// Helper function to get description
function getDescription(cssVariable: string, isAlias: boolean): string {
  if (isAlias) {
    const parts = cssVariable.replace('--apl-alias-color-', '').split('-');
    const category = parts[0];
    const role = parts.slice(1).join('-');
    return `Semantic color for ${category} ${role}`;
  } else {
    const parts = cssVariable.replace('--apl-base-color-', '').split('-');
    const category = parts[0];
    const shade = parts[parts.length - 1];
    return `Base color for ${category} palette, shade ${shade}`;
  }
}

// Parse CSS variables and extract color tokens
function parseColorTokens(): Record<string, ColorToken[]> {
  const lines = CSS_VARIABLES.split('\n');
  const baseColors: ColorToken[] = [];
  const aliasColors: ColorToken[] = [];

  for (const line of lines) {
    const trimmed = line.trim();
    if (!trimmed || trimmed.startsWith('//')) continue;

    const match = trimmed.match(/^(--apl-(?:base|alias)-color-[^:]+):\s*(.+);?$/);
    if (!match) continue;

    const [, cssVariable, value] = match;
    const hexValue = resolveColorValue(value);

    // Skip if we couldn't resolve to a hex value
    if (!hexValue.startsWith('#')) continue;

    const isBase = cssVariable.includes('base-color');
    const isAlias = cssVariable.includes('alias-color');

    if (isBase) {
      baseColors.push({
        name: formatDisplayName(cssVariable, false),
        cssVariable,
        hexValue,
        category: 'base',
        subcategory: getSubcategory(cssVariable, false),
        description: getDescription(cssVariable, false)
      });
    } else if (isAlias) {
      aliasColors.push({
        name: formatDisplayName(cssVariable, true),
        cssVariable,
        hexValue,
        category: 'alias',
        subcategory: getSubcategory(cssVariable, true),
        description: getDescription(cssVariable, true)
      });
    }
  }

  return {
    base: baseColors,
    alias: aliasColors
  };
}

export function getColorTokens(): Record<string, ColorToken[]> {
  return parseColorTokens();
}

export const categoryTitles = {
  base: "Base (Global)",
  alias: "Alias (Semantic)"
}
