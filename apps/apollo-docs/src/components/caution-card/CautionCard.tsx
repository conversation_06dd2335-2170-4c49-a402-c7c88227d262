import { Warning } from "@design-systems/apollo-icons"
import React from "react"

const CautionCard = ({ children }: { children: React.ReactNode }) => {
  return (
    <div
      style={{
        padding: "16px",
        marginBottom: "24px",
        backgroundColor: "#fff3cd",
        border: "1px solid #ffeaa7",
        borderRadius: "8px",
        borderLeft: "4px solid #fdcb6e",
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          gap: "8px",
          marginBottom: "8px",
        }}
      >
        <span
          style={{
            fontSize: "16px",
            fontWeight: 700,
            color: "#4d3800",
            textTransform: "uppercase",
            display: "flex",
            alignItems: "center",
            gap: "4px",
          }}
        >
          <Warning /> caution
        </span>
      </div>
      <p
        style={{
          margin: "0",
          color: "#4d3800",
          fontSize: "16px",
          lineHeight: "1.5",
        }}
      >
        {children}
      </p>
    </div>
  )
}

export default CautionCard
