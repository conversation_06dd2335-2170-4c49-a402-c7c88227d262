import React, { useEffect, useMemo, useRef, useState } from "react"
import { ApolloToken } from "@apollo/token"
import {
  Accordion,
  <PERSON>ert,
  Badge,
  Button,
  Checkbox,
  Chip,
  createThemeV2,
  Input,
  Modal,
  Pagination,
  Radio,
  RadioGroup,
  Select,
  Switch,
  Tabs,
  Theme,
  Typography,
  UploadBox,
} from "@apollo/ui"
import type { ThemeConfig } from "@apollo/ui"
import { InfoCircle, Search } from "@design-systems/apollo-icons"

// Small helpers
const toPx = (n: number) => `${Math.round(n)}px`

// Resolve a token value to a pixel number, supporting "{...}" references
function resolvePx(val: any): number | null {
  if (typeof val === "number") return val
  if (typeof val === "string") {
    const direct = parseFloat(val.replace(/px$/, ""))
    if (!isNaN(direct)) return direct
    const m = val.match(/^\{(.+)\}$/)
    if (m) {
      const path = m[1].split(".")
      let obj: any = ApolloToken as any
      for (const p of path) {
        if (obj && p in obj) obj = obj[p]
        else return null
      }
      if (typeof obj === "string") {
        const px = parseFloat(obj.replace(/px$/, ""))
        return isNaN(px) ? null : px
      }
    }
  }
  return null
}

// Resolve a token color (hex) from direct value or "{...}" path
function resolveColor(val: any): string | null {
  if (typeof val === "string") {
    // direct hex or rgb
    if (!val.startsWith("{")) return val
    const m = val.match(/^\{(.+)\}$/)
    if (m) {
      const path = m[1].split(".")
      let obj: any = ApolloToken as any
      for (const p of path) {
        if (obj && p in obj) obj = obj[p]
        else return null
      }
      return typeof obj === "string" ? obj : null
    }
  }
  return null
}

// Try to infer which global color family a base semantic palette maps to
function detectDefaultFamily(category: SemanticCategory): string {
  const families = Object.keys((ApolloToken as any)?.color || {}) as string[]
  const baseCat = (ApolloToken as any)?.base?.color?.[category] || {}
  const sampleShades = ["40", "50", "60", "90"]
  for (const family of families) {
    let match = true
    for (const shade of sampleShades) {
      const baseVal =
        resolveColor((baseCat as any)?.[shade]) || (baseCat as any)?.[shade]
      const famVal = (ApolloToken as any)?.color?.[family]?.[shade]
      if (
        !baseVal ||
        !famVal ||
        String(baseVal).toLowerCase() !== String(famVal).toLowerCase()
      ) {
        match = false
        break
      }
    }
    if (match) return family
  }
  // Fallback to first family if exact match not found
  return families[0] || ""
}

// Extract available global color families from tokens
const COLOR_FAMILIES = Object.keys(ApolloToken.color || {}) as Array<
  keyof typeof ApolloToken.color
>

// Shades commonly used across semantic mappings
const COMMON_SHADES = [
  "0",
  "10",
  "20",
  "30",
  "40",
  "50",
  "60",
  "70",
  "80",
  "90",
  "95",
  "99",
  "100",
] as const

type SemanticCategory =
  | "primary"
  | "secondary"
  | "tertiary"
  | "success"
  | "warning"
  | "danger"
  | "neutral"

function deepMerge<T extends any, U extends any>(base: T, patch: U): T & U {
  // Recursively merge plain objects; arrays and primitives are replaced by patch
  if (Array.isArray(base) || Array.isArray(patch)) return patch as any as T & U
  if (base && typeof base === "object" && patch && typeof patch === "object") {
    const out: any = { ...(base as any) }
    for (const key of Object.keys(patch as any)) {
      const b = (base as any)[key]
      const p = (patch as any)[key]
      if (
        b &&
        typeof b === "object" &&
        !Array.isArray(b) &&
        p &&
        typeof p === "object" &&
        !Array.isArray(p)
      ) {
        out[key] = deepMerge(b, p)
      } else {
        out[key] = p
      }
    }
    return out as T & U
  }
  return patch as any as T & U
}

export default function ThemeEditor() {
  const [mode, setMode] = useState<"light" | "dark">("light")

  // Responsive: stack panels on small screens
  const [isNarrow, setIsNarrow] = useState(false)
  useEffect(() => {
    const mq: any = window.matchMedia("(max-width: 1024px)")
    const onChange = (e: any) => setIsNarrow(e.matches)
    setIsNarrow(mq.matches)
    if (mq.addEventListener) mq.addEventListener("change", onChange)
    else mq.addListener(onChange)
    return () => {
      if (mq.removeEventListener) mq.removeEventListener("change", onChange)
      else mq.removeListener(onChange)
    }
  }, [])

  // Per-feature states
  const [themeTokens, setThemeTokens] = useState<ThemeConfig["tokens"]>({})

  // Typography baseline - the 'xs' font size that all other sizes scale from
  const defaultXs = (() => {
    const raw = (ApolloToken as any)?.base?.typography?.["font-size"]?.xs
    return resolvePx(raw) ?? 16
  })()
  const [baseline, setBaseline] = useState<number>(defaultXs)


  // Track selected color families for each category (default inferred from ApolloToken)
  const inferredDefaults = useMemo<Record<SemanticCategory, string>>(
    () => ({
      primary: detectDefaultFamily("primary"),
      secondary: detectDefaultFamily("secondary"),
      tertiary: detectDefaultFamily("tertiary"),
      success: detectDefaultFamily("success"),
      warning: detectDefaultFamily("warning"),
      danger: detectDefaultFamily("danger"),
      neutral: detectDefaultFamily("neutral"),
    }),
    []
  )
  const [selectedColorFamilies, setSelectedColorFamilies] = useState<
    Record<SemanticCategory, string>
  >(() => inferredDefaults)

  // Spacing base step (in px). Default 2px increments (2,4,6,...)
  const [spacingBaseStep, setSpacingBaseStep] = useState<number>(2)

  // Import/Export dialog state
  const [showConfigDialog, setShowConfigDialog] = useState(false)
  const [configText, setConfigText] = useState<string>("")
  const [configError, setConfigError] = useState<string | null>(null)
  const [isOpenModal, setIsOpenModal] = useState(false)

  // File import for config
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  function triggerFileImport() {
    fileInputRef.current?.click()
  }
  async function handleFilePicked(e: React.ChangeEvent<HTMLInputElement>) {
    const f = e.target.files?.[0]
    if (!f) return
    try {
      const text = await f.text()
      try {
        const parsed = JSON.parse(text)
        setConfigText(JSON.stringify(parsed, null, 2))
      } catch {
        // If not valid JSON, keep raw text
        setConfigText(text)
      }
      setConfigError(null)
    } catch (err: any) {
      setConfigError(err?.message || "Failed to read file")
    } finally {
      // reset input so the same file can be selected again
      e.target.value = ""
    }
  }

  // Border radius base step (in px). Default 2px increments
  const [radiusBaseStep, setRadiusBaseStep] = useState<number>(2)

  // Accordion states
  const [accordionStates, setAccordionStates] = useState({
    mode: true,
    colors: true,
    typography: false,
    spacing: false,
    radius: false,
  })

  // Derived font-size overrides based on baseline to match Figma design
  const fontSizeOverrides = useMemo(() => {
    const currentBaseline = baseline || 16

    // Font size mapping based on Figma design with proportional scaling from baseline
    // These ratios ensure consistent scaling across all font sizes when baseline changes
    // Ratios calculated from original Apollo token values (4xs=10, 3xs=12, 2xs=14, xs=16, etc.)
    const fontSizeMap: Record<string, number> = {
      "4xs": Math.round(currentBaseline * 0.625),   // 10/16 = 0.625
      "3xs": Math.round(currentBaseline * 0.75),    // 12/16 = 0.75
      "2xs": Math.round(currentBaseline * 0.875),   // 14/16 = 0.875
      "xs": currentBaseline,                        // 16/16 = 1.0 (baseline)
      "xs-sm": Math.round(currentBaseline * 1.125), // 18/16 = 1.125
      "xs-md": Math.round(currentBaseline * 1.25),  // 20/16 = 1.25
      "xs-plus": Math.round(currentBaseline * 1.375), // 22/16 = 1.375
      "sm": Math.round(currentBaseline * 1.5),      // 24/16 = 1.5
      "md": Math.round(currentBaseline * 1.75),     // 28/16 = 1.75
      "lg": Math.round(currentBaseline * 2),        // 32/16 = 2.0
      "xl": Math.round(currentBaseline * 2.25),     // 36/16 = 2.25
      "2xl": Math.round(currentBaseline * 2.8125),  // 45/16 = 2.8125
      "3xl": Math.round(currentBaseline * 3.5625),  // 57/16 = 3.5625
    }

    const result: Record<string, string> = {}
    Object.entries(fontSizeMap).forEach(([key, value]) => {
      result[key] = toPx(value)
    })

    return result
  }, [baseline])

  // Spacing overrides based on base step scale
  const spacingOverrides = useMemo(() => {
    const DEFAULT_STEP = 2
    const SCALE = (spacingBaseStep || DEFAULT_STEP) / DEFAULT_STEP
    const baseSpacing = ApolloToken.base.spacing || {}
    const result: Record<string, string> = {}
    Object.entries(baseSpacing).forEach(([k, v]) => {
      const n = resolvePx(v)
      if (typeof n === "number" && !isNaN(n)) result[k] = toPx(n * SCALE)
    })
    return result
  }, [spacingBaseStep])

  // Border radius overrides based on base step scale
  const radiusOverrides = useMemo(() => {
    const DEFAULT_STEP = 2
    const SCALE = (radiusBaseStep || DEFAULT_STEP) / DEFAULT_STEP
    const baseRadius = (ApolloToken as any)?.base?.radius || {}
    const result: Record<string, string> = {}
    Object.entries(baseRadius).forEach(([k, v]) => {
      const n = resolvePx(v)
      if (typeof n === "number" && !isNaN(n)) result[k] = toPx(n * SCALE)
    })
    return result
  }, [radiusBaseStep])

  const themeConfig: ThemeConfig = useMemo(() => {
    return {
      inherit: true,
      tokens: {
        // Typography mappings
        base: {
          ...themeTokens?.base,
          typography: {
            "font-size": fontSizeOverrides,
          },
          // Spacing

          spacing: spacingOverrides,
          // Border Radius
          radius: radiusOverrides,
        },
      },
    }
  }, [themeTokens, fontSizeOverrides, spacingOverrides, radiusOverrides])

  // Handlers
  function handlePaletteChange(
    category: SemanticCategory,
    family: keyof typeof ApolloToken.color
  ) {
    setThemeTokens((prev) =>
      deepMerge(prev || {}, {
        base: {
          color: {
            [category]: ApolloToken.color[family],
          },
        },
      } as any)
    )
    setSelectedColorFamilies((prev) => ({
      ...prev,
      [category]: String(family),
    }))
  }

  function handleShadeChange(
    category: SemanticCategory,
    shade: (typeof COMMON_SHADES)[number],
    value: string
  ) {
    setThemeTokens((prev) =>
      deepMerge(prev || {}, {
        base: {
          color: {
            [category]: {
              [shade]: value,
            },
          },
        },
      } as any)
    )
  }

  // Import/Export helpers
  function openConfigDialog(prefillFromTheme = true) {
    setConfigError(null)
    try {
      const payload = prefillFromTheme
        ? buildEditedTokensPayload()
        : { tokens: {} }
      if (payload) {
        setConfigText(JSON.stringify(payload, null, 2))
      }
    } catch {
      // On any error, do not overwrite existing text
    }
    setShowConfigDialog(true)
  }

  // Helpers to construct a minimal payload of only edited tokens
  function isEmptyObject(obj: any) {
    return !obj || (typeof obj === "object" && Object.keys(obj).length === 0)
  }
  function buildEditedTokensPayload() {
    const base: any = {}

    // Colors edited via themeTokens
    const editedColor = (themeTokens as any)?.base?.color
    if (!isEmptyObject(editedColor)) {
      base.color = editedColor
    }

    // Typography edited via baseline slider or manual edits
    const editedTypography = (themeTokens as any)?.base?.typography
    if (baseline !== defaultXs) {
      base.typography = {
        ...(editedTypography || {}),
        "font-size": fontSizeOverrides,
      }
    } else if (!isEmptyObject(editedTypography)) {
      base.typography = editedTypography
    }

    // Spacing and Radius edited via sliders
    if (spacingBaseStep !== 2) {
      base.spacing = spacingOverrides
    }
    if (radiusBaseStep !== 2) {
      base.radius = radiusOverrides
    }

    if (isEmptyObject(base)) return null
    return { tokens: { base } }
  }

  function exportTokens() {
    const data = buildEditedTokensPayload()
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json;charset=utf-8",
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = "apollo-theme-tokens.json"
    document.body.appendChild(a)
    a.click()
    a.remove()
    URL.revokeObjectURL(url)
  }

  function importTokensFromText() {
    setConfigError(null)
    try {
      const parsed = JSON.parse(configText || "{}")
      const imported = parsed?.tokens ?? parsed
      if (!imported || typeof imported !== "object") {
        throw new Error(
          "Invalid payload: expected { tokens: {...} } or tokens object"
        )
      }
      setThemeTokens((prev) => deepMerge(prev || {}, imported as any))
      setShowConfigDialog(false)
    } catch (e: any) {
      setConfigError(e?.message || "Failed to parse JSON")
    }
  }

  return (
    <div
      style={
        {
          display: "grid",
          gridTemplateColumns: isNarrow ? "1fr" : "minmax(320px, 360px) 1fr",
          gap: isNarrow ? 24 : 32,
          minHeight: "100vh",
          background:
            "var(--apl-alias-color-background-and-surface-background, #ffffff)",
          padding: isNarrow ? 16 : 24,
        } as React.CSSProperties
      }
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 16,
          background:
            "var(--apl-alias-color-background-and-surface-surface, #f9f9f9)",
          borderRadius: 12,
          padding: 24,
          boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
          height: "fit-content",
        }}
      >
        <div style={{ marginBottom: 8 }}>
          <Typography level="headlineMedium">Theme Editor</Typography>
          <Typography level="bodyMedium" style={{ opacity: 0.7 }}>
            Customize your design system tokens and see changes in real-time
          </Typography>
        </div>
        <div
          style={{
            display: "flex",
            gap: 8,
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Button
            size="small"
            variant="outline"
            onClick={() => openConfigDialog(true)}
          >
            Config
          </Button>
          <Switch
            checked={mode === "dark"}
            onCheckedChange={(checked) => setMode(checked ? "dark" : "light")}
            actionText="Dark Mode"
          />
        </div>
        <Accordion
          label={
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                width: "100%",
              }}
            >
              <span>Color</span>

              <Button
                variant="text"
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  setThemeTokens((prev) => {
                    const newTokens = { ...prev } as any
                    if (newTokens.base?.color) {
                      delete newTokens.base.color
                    }
                    return newTokens
                  })
                  setSelectedColorFamilies(inferredDefaults)
                }}
                style={{ fontSize: "12px", padding: "4px 8px" }}
              >
                Reset
              </Button>
            </div>
          }
          defaultOpen={accordionStates.colors}
          onOpenChange={(open) =>
            setAccordionStates((prev) => ({ ...prev, colors: open }))
          }
          borderless
        >
          <div style={{ overflowY: "auto", maxHeight: 300 }}>
            <div
              style={{
                padding: "16px 0",
                display: "flex",
                flexDirection: "column",
                gap: 16,
              }}
            >
              {(
                [
                  "primary",
                  "secondary",
                  "tertiary",
                  "neutral",
                  "danger",
                  "warning",
                  "success",
                ] as SemanticCategory[]
              ).map((cat) => (
                <Accordion
                  key={cat}
                  label={
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: 12,
                        }}
                      >
                        <div
                          style={{
                            width: 48,
                            height: 48,
                            borderRadius: 4,
                            background:
                              resolveColor(
                                (themeTokens as any)?.base?.color?.[cat]?.[
                                  cat === "neutral"
                                    ? "30"
                                    : cat === "primary" || cat === "danger"
                                      ? "40"
                                      : "50"
                                ]
                              ) ||
                              resolveColor(
                                (ApolloToken as any)?.color?.[
                                  selectedColorFamilies[cat] as any
                                ]?.[
                                  cat === "neutral"
                                    ? "30"
                                    : cat === "primary" || cat === "danger"
                                      ? "40"
                                      : "50"
                                ]
                              ) ||
                              resolveColor(
                                (ApolloToken as any)?.base?.color?.[cat]?.[
                                  cat === "neutral"
                                    ? "30"
                                    : cat === "primary" || cat === "danger"
                                      ? "40"
                                      : "50"
                                ]
                              ) ||
                              "#cccccc",
                            border:
                              "1px solid var(--apl-alias-color-outline-and-border-outline, #d0d0d0)",
                          }}
                        />
                        <div
                          style={{ display: "flex", flexDirection: "column" }}
                        >
                          <span
                            style={{
                              textTransform: "capitalize",
                              fontWeight: 500,
                            }}
                          >
                            {cat}
                          </span>
                          <span style={{ fontSize: "11px", opacity: 0.6 }}>
                            {Object.keys(
                              (themeTokens as any)?.base?.color?.[cat] || {}
                            ).length ||
                              Object.keys(
                                (ApolloToken as any)?.base?.color?.[cat] || {}
                              ).length}{" "}
                            shades
                          </span>
                        </div>
                      </div>
                      <Button
                        variant="text"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation()
                          setThemeTokens((prev) => {
                            const newTokens = { ...prev } as any
                            if (newTokens.base?.color?.[cat]) {
                              delete newTokens.base.color[cat]
                            }
                            return newTokens
                          })
                          setSelectedColorFamilies((prev) => ({
                            ...prev,
                            [cat]: inferredDefaults[cat],
                          }))
                        }}
                        style={{ fontSize: "11px", padding: "2px 6px" }}
                      >
                        Reset
                      </Button>
                    </div>
                  }
                  defaultOpen={false}
                  borderless
                  style={{
                    background:
                      "var(--apl-alias-color-background-and-surface-background, #ffffff)",
                    borderRadius: 8,
                    border:
                      "1px solid var(--apl-alias-color-outline-and-border-outline-variant, #e5e5e5)",
                  }}
                >
                  <div
                    style={{
                      overflowY: "auto",
                      maxHeight: 300,
                    }}
                  >
                    <div style={{ marginBottom: 20 }}>
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          marginBottom: 8,
                        }}
                      >
                        <Typography level="labelMedium">
                          Base Palette
                        </Typography>
                      </div>
                      <Select
                        placeholder={`Select color family (default: ${inferredDefaults[cat]})`}
                        value={selectedColorFamilies[cat]}
                        onChange={(value) =>
                          handlePaletteChange(
                            cat,
                            value as keyof typeof ApolloToken.color
                          )
                        }
                        fullWidth
                      >
                        {COLOR_FAMILIES.map((family) => (
                          <Select.Option
                            key={String(family)}
                            label={String(family)}
                            value={String(family)}
                          />
                        ))}
                      </Select>
                    </div>

                    <div>
                      <Typography
                        level="labelMedium"
                        style={{ marginBottom: 12 }}
                      >
                        Color Shades
                      </Typography>
                      <div
                        style={{
                          display: "grid",
                          gridTemplateColumns: "1fr 1fr 1fr 1fr",
                          gap: 8,
                        }}
                      >
                        {COMMON_SHADES.map((shade) => (
                          <div
                            key={shade}
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              gap: 6,
                            }}
                          >
                            <Typography
                              level="labelSmall"
                              style={{
                                opacity: 0.7,
                                textAlign: "center",
                                fontSize: "11px",
                              }}
                            >
                              {shade}
                            </Typography>
                            <input
                              type="color"
                              onChange={(e) =>
                                handleShadeChange(cat, shade, e.target.value)
                              }
                              value={
                                resolveColor(
                                  (themeTokens as any)?.base?.color?.[cat]?.[
                                    shade
                                  ]
                                ) ||
                                resolveColor(
                                  (ApolloToken as any)?.color?.[
                                    selectedColorFamilies[cat] as any
                                  ]?.[shade]
                                ) ||
                                resolveColor(
                                  (ApolloToken as any)?.base?.color?.[cat]?.[
                                    shade
                                  ]
                                ) ||
                                "#ffffff"
                              }
                              style={{
                                width: "100%",
                                height: 28,
                                padding: 0,
                                border:
                                  "1px solid var(--apl-alias-color-outline-and-border-outline, #d0d0d0)",
                                borderRadius: 4,
                                cursor: "pointer",
                              }}
                            />
                            <input
                              type="text"
                              value={
                                resolveColor(
                                  (themeTokens as any)?.base?.color?.[cat]?.[
                                    shade
                                  ]
                                ) ||
                                resolveColor(
                                  (ApolloToken as any)?.color?.[
                                    selectedColorFamilies[cat] as any
                                  ]?.[shade]
                                ) ||
                                resolveColor(
                                  (ApolloToken as any)?.base?.color?.[cat]?.[
                                    shade
                                  ]
                                ) ||
                                "#ffffff"
                              }
                              onChange={(e) =>
                                handleShadeChange(cat, shade, e.target.value)
                              }
                              placeholder="#000000"
                              style={{
                                width: "100%",
                                padding: "2px 4px",
                                fontSize: "10px",
                                border:
                                  "1px solid var(--apl-alias-color-outline-and-border-outline-variant, #e5e5e5)",
                                borderRadius: 3,
                                textAlign: "center",
                                fontFamily: "monospace",
                                background:
                                  "var(--apl-alias-color-background-and-surface-background, #ffffff)",
                              }}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </Accordion>
              ))}
            </div>
          </div>
        </Accordion>

        <Accordion
          label={
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                width: "100%",
              }}
            >
              <span>Typography</span>
              <Button
                variant="text"
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  setBaseline(defaultXs)
                  setThemeTokens((prev) => {
                    const newTokens = { ...prev } as any
                    if (newTokens.base?.typography) {
                      delete newTokens.base.typography
                    }
                    return newTokens
                  })
                }}
                style={{ fontSize: "12px", padding: "4px 8px" }}
              >
                Reset
              </Button>
            </div>
          }
          defaultOpen={accordionStates.typography}
          onOpenChange={(open) =>
            setAccordionStates((prev) => ({ ...prev, typography: open }))
          }
          borderless
        >
          <div
            style={{
              padding: "16px 0",
              display: "flex",
              flexDirection: "column",
              gap: 20,
            }}
          >
            <div>
              <Typography level="labelMedium" style={{ marginBottom: 8 }}>
                Baseline Font Size : {baseline}px
              </Typography>
              <input
                type="range"
                min={12}
                max={28}
                step={1}
                value={baseline}
                onChange={(e) => setBaseline(parseInt(e.target.value, 10))}
                style={{
                  width: "100%",
                  height: 6,
                  borderRadius: 3,
                  background:
                    "var(--apl-alias-color-outline-and-border-outline-variant, #e5e5e5)",
                  outline: "none",
                  cursor: "pointer",
                }}
              />
              <div style={{
                display: "flex",
                justifyContent: "space-between",
                marginTop: 8,
                fontSize: "11px",
                opacity: 0.6
              }}>
                <span>12px</span>
                <span>20px</span>
                <span>28px</span>
              </div>
            </div>

            {/* Font Size Preview */}
            <div>
              <Typography level="labelMedium" style={{ marginBottom: 12 }}>
                Font Size Preview
              </Typography>
              <div style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: 8,
                fontSize: "11px"
              }}>
                {Object.entries(fontSizeOverrides).map(([key, value]) => (
                  <div key={key} style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "6px 8px",
                    background: "var(--apl-alias-color-background-and-surface-background, #ffffff)",
                    borderRadius: 4,
                    border: "1px solid var(--apl-alias-color-outline-and-border-outline-variant, #e5e5e5)"
                  }}>
                    <span style={{ opacity: 0.7, fontSize: "10px", minWidth: "40px" }}>{key}</span>
                    <span style={{ fontWeight: 500, fontSize: "10px" }}>{value}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Accordion>

        <Accordion
          label={
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                width: "100%",
              }}
            >
              <span>Spacing</span>
              <Button
                variant="text"
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  setSpacingBaseStep(2)
                  setThemeTokens((prev) => {
                    const newTokens = { ...prev } as any
                    if (newTokens.base?.spacing) {
                      delete newTokens.base.spacing
                    }
                    return newTokens
                  })
                }}
                style={{ fontSize: "12px", padding: "4px 8px" }}
              >
                Reset
              </Button>
            </div>
          }
          defaultOpen={accordionStates.spacing}
          onOpenChange={(open) =>
            setAccordionStates((prev) => ({ ...prev, spacing: open }))
          }
          borderless
        >
          <div
            style={{
              padding: "16px 0",
              display: "flex",
              flexDirection: "column",
              gap: 20,
            }}
          >
            <div>
              <Typography level="labelMedium" style={{ marginBottom: 8 }}>
                Spacing Size Base Step: {spacingBaseStep}px
              </Typography>

              <input
                type="range"
                min={1}
                max={16}
                step={1}
                value={spacingBaseStep}
                onChange={(e) => setSpacingBaseStep(parseFloat(e.target.value))}
                style={{
                  width: "100%",
                  height: 6,
                  borderRadius: 3,
                  background:
                    "var(--apl-alias-color-outline-and-border-outline-variant, #e5e5e5)",
                  outline: "none",
                  cursor: "pointer",
                }}
              />
            </div>
          </div>
        </Accordion>
        <Accordion
          label={
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                width: "100%",
              }}
            >
              <span>Border Radius</span>
              <Button
                variant="text"
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  setRadiusBaseStep(2)
                  setThemeTokens((prev) => {
                    const newTokens = { ...prev } as any
                    if (newTokens.base?.radius) {
                      delete newTokens.base.radius
                    }
                    return newTokens
                  })
                }}
                style={{ fontSize: "12px", padding: "4px 8px" }}
              >
                Reset
              </Button>
            </div>
          }
          defaultOpen={accordionStates.radius}
          onOpenChange={(open) =>
            setAccordionStates((prev) => ({ ...prev, radius: open }))
          }
          borderless
        >
          <div
            style={{
              padding: "16px 0",
              display: "flex",
              flexDirection: "column",
              gap: 20,
            }}
          >
            <div>
              <Typography level="labelMedium" style={{ marginBottom: 8 }}>
                Border Radius Base Step: {radiusBaseStep}px
              </Typography>
              <input
                type="range"
                min={1}
                max={6}
                step={1}
                value={radiusBaseStep}
                onChange={(e) => setRadiusBaseStep(parseFloat(e.target.value))}
                style={{
                  width: "100%",
                  height: 6,
                  borderRadius: 3,
                  background:
                    "var(--apl-alias-color-outline-and-border-outline-variant, #e5e5e5)",
                  outline: "none",
                  cursor: "pointer",
                }}
              />
            </div>
          </div>
        </Accordion>
      </div>

      <div
        style={{
          borderRadius: 12,
          boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
          height: "fit-content",
          width: "100%",
        }}
      >
        <Theme theme={createThemeV2(themeConfig)} mode={mode}>
          <div
            style={{
              display: "grid",
              gap: 16,
            }}
          >
            <div
              style={{
                background:
                  "var(--apl-alias-color-background-and-surface-background, #ffffff)",
                borderRadius: 8,
                padding: 16,
                border:
                  "1px solid var(--apl-alias-color-outline-and-border-outline-variant, #e5e5e5)",
              }}
            >
              {/* Header: Avatar + Name + Action button */}
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: 16,
                }}
              >
                <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
                  <div style={{ position: "relative" }}>
                    <div
                      style={{
                        width: 36,
                        height: 36,
                        borderRadius: "var(--apl-alias-radius-radius11)",
                        background: "var(--apl-alias-color-primary-primary)",
                        color: "var(--apl-alias-color-primary-on-primary)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontWeight: 700,
                      }}
                    >
                      CE
                    </div>
                  </div>
                  <div>
                    <Typography level="titleMedium" color="default">
                      Cameron Evans
                    </Typography>

                    <Typography
                      level="labelSmall"
                      color="default"
                      style={{ opacity: 0.7 }}
                    >
                      Senior Researcher at Contoso
                    </Typography>
                  </div>
                </div>
                <div
                  style={{
                    display: "flex",
                    gap: 12,
                    flexWrap: "wrap",
                    margin: "16px 0",
                  }}
                >
                  <Badge label="Default" />
                  <Badge label="Completed" color="success" />
                  <Badge label="Warning" color="warning" />
                  <Badge label="Error" color="error" />
                </div>
              </div>
              <div style={{ display: "flex", gap: 12, flexWrap: "wrap" }}>
                <Button color="primary">Filled</Button>
                <Button variant="outline" color="primary">
                  Outline
                </Button>
                <Button variant="text" color="primary">
                  Text
                </Button>
                <Button color="negative">Filled</Button>
                <Button variant="outline" color="negative">
                  Outline
                </Button>
                <Button variant="text" color="negative">
                  Text
                </Button>
              </div>
              {/* Tabs row */}
              <div
                style={{
                  padding: 16,
                }}
              >
                <Tabs.Root defaultValue="home">
                  <Tabs.List>
                    <Tabs.Tab value="home">Home</Tabs.Tab>
                    <Tabs.Tab value="pages">Pages</Tabs.Tab>
                    <Tabs.Tab value="documents">Documents</Tabs.Tab>
                  </Tabs.List>
                </Tabs.Root>
              </div>

              {/* Content grid */}
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(auto-fit, minmax(240px, 1fr))",
                  gap: 20,
                }}
              >
                {/* Left column */}
                <div style={{ display: "grid", gap: 16 }}>
                  <Input
                    placeholder="Find"
                    startDecorator={<Search />}
                    fullWidth
                  />

                  <Select placeholder="Select" fullWidth>
                    <Select.Option label="Apple" value="apple" />
                    <Select.Option label="Banana" value="banana" />
                    <Select.Option label="Cherry" value="cherry" />
                  </Select>

                  <div
                    style={{ display: "flex", alignItems: "center", gap: 12 }}
                  >
                    <Checkbox label={"Option 1"} defaultChecked />
                    <Checkbox label={"Option 2"} />
                  </div>

                  <RadioGroup
                    name="opts"
                    defaultValue="1"
                    direction="horizontal"
                  >
                    <Radio value="1">Option 1</Radio>
                    <Radio value="2">Option 2</Radio>
                  </RadioGroup>
                </div>

                {/* Right column */}
                <div style={{ display: "grid", gap: 16 }}>
                  <div
                    style={{ display: "flex", alignItems: "center", gap: 16 }}
                  >
                    <Switch checked actionText="On" />
                    <Switch actionText="Off" />
                  </div>

                  <div
                    style={{ display: "flex", alignItems: "center", gap: 12 }}
                  >
                    <Input
                      label="Email"
                      required
                      placeholder="Enter email"
                      fullWidth
                      error
                      helperText="Please enter a valid email address"
                    />
                  </div>
                  <div style={{ display: "flex", gap: 12, flexWrap: 'wrap' }}>
                  <Button onClick={() => setIsOpenModal(true)}>Open Modal</Button>
                  <Button
                    variant="text"
                    href="https://www.google.com"
                    target="_blank"
                    rel="noreferrer"
                  >
                    Example link
                  </Button>
                  </div>
                </div>
              </div>
              <div
                style={{
                  display: "flex",
                  gap: 8,
                  flexWrap: "wrap",
                  padding: 16,
                }}
              >
                <Chip label="Default" />
                <Chip label="Negative" color="negative" />
                <Chip label="Default" variant="outline" />
                <Chip label="Negative" color="negative" variant="outline" />
                <Chip label="Default" onClose={() => null} />
                <Chip label="Default" onCheck={() => null} />
              </div>
              <div
                style={{
                  display: "flex",
                  gap: 8,
                  flexWrap: "wrap",
                  padding: 16,
                }}
              >
                <Alert
                  type="success"
                  title="Success"
                  description="This is a success alert"
                />
                <Alert
                  type="information"
                  title="Information"
                  description="This is an informational alert"
                />
                <Alert
                  type="warning"
                  title="Warning"
                  description="This is a warning alert"
                />
                <Alert
                  type="error"
                  title="Error"
                  description="This is an error alert"
                />
              </div>
              <Pagination
                count={10}
                defaultPage={5}
                style={{
                  marginBottom: 16,
                }}
              />
              <UploadBox label="Upload File" fullWidth />
            </div>
          </div>
          <Modal.Root open={isOpenModal} onOpenChange={setIsOpenModal}>
            <Modal.Header icon={<InfoCircle />}>Modal Title</Modal.Header>
            <Modal.Content>
              <div>
                Once upon a time, there was a forest where plenty of birds lived
                and built their nests on the trees.
              </div>
            </Modal.Content>
            <Modal.Footer>
              <Button onClick={() => setIsOpenModal(false)}>OK</Button>
            </Modal.Footer>
          </Modal.Root>
        </Theme>
      </div>
      {showConfigDialog && (
        <Modal.Root
          open={showConfigDialog}
          onOpenChange={(open) => setShowConfigDialog(open)}
          className="max-w-[720px] w-[92vw]"
        >
          <Modal.Header>
            <Typography level="titleLarge">Config</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography
              level="bodySmall"
              style={{ opacity: 0.75, marginBottom: 8 }}
            >
              Paste or edit a tokens JSON payload. You can export the current
              tokens or import to replace the working config.
            </Typography>
            <textarea
              value={configText}
              onChange={(e) => setConfigText(e.target.value)}
              spellCheck={false}
              style={{
                width: "100%",
                height: 280,
                resize: "vertical",
                fontFamily:
                  "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace",
                fontSize: 12,
                lineHeight: 1.5,
                padding: 12,
                borderRadius: 8,
                border: "1px solid var(--apl-alias-color-border-default, #ccc)",
                background:
                  "var(--apl-alias-color-background-and-surface-background, #fff)",
                color: "inherit",
                boxSizing: "border-box",
              }}
            />
            <input
              ref={fileInputRef}
              type="file"
              accept="application/json,.json"
              onChange={handleFilePicked}
              style={{ display: "none" }}
            />
            {configError && (
              <div style={{ marginTop: 8 }}>
                <Alert
                  color="error"
                  title="Import error"
                  description={configError}
                />
              </div>
            )}
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={triggerFileImport}>
              Import
            </Button>
            <Button variant="outline" onClick={exportTokens}>
              Export
            </Button>
            <div style={{ flex: 1 }} />
            <Button
              variant="outline"
              onClick={() => setShowConfigDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={importTokensFromText}>OK</Button>
          </Modal.Footer>
        </Modal.Root>
      )}
    </div>
  )
}
