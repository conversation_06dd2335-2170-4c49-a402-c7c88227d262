import { Typography } from "@apollo/ui";
import { ArrowRight } from "@design-systems/apollo-icons";
import { hrefTo } from "@storybook/addon-links";
import { useEffect, useState } from "react";

export default function ResourceCard({ page, title, description, icon, fullSpan }: {
  page: string;
  title: string;
  description: string;
  icon?: React.ReactNode;
  fullSpan?: boolean;
}) {
  const [url, setUrl] = useState("#")
  const [hovered, setHovered] = useState(false)
  useEffect(() => {
    let mounted = true
    ;(async () => {
      try {
        const href = await hrefTo(page, "")
        if (mounted) setUrl(href)
      } catch {}
    })()
    return () => {
      mounted = false
    }
  }, [page])

return (

<a
  href={url}
  onMouseEnter={() => setHovered(true)}
  onMouseLeave={() => setHovered(false)}
  style={{
    borderRadius: 16,
    border: "1px solid var(--sb-ui-border-color)",
    padding: "20px 20px 16px 20px",
    textDecoration: "none",
    color: "var(--sb-primary-text-color)",
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    minHeight: 140,
    gap: 12,
    background: hovered ? "var(--sb-allgrey-background-color)" : "transparent",
    gridColumn: fullSpan ? "1 / -1" : undefined,
    transition: "background 120ms ease",
  }}
>
  <div style={{ display: "flex", gap: 12, alignItems: "flex-start" }}>
    {icon && (
      <div
        style={{
          width: 36,
          height: 36,
          borderRadius: 12,
          border: "1px solid var(--sb-ui-border-color)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexShrink: 0,
        }}
      >
        {icon}
      </div>
    )}
    <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
      <Typography level="bodyLarge" style={{ margin: 0, fontWeight: 600 }}>
        {title}
      </Typography>
      <div style={{ color: "var(--sb-secondary-text-color)" }}>
        {description}
      </div>
    </div>
  </div>
  <div
    style={{
      alignSelf: "flex-end",
      fontWeight: 500,
      color: "var(--sb-link-color)",
      display: "flex",
      alignItems: "center",
      gap: 6,
    }}
  >
    Open <ArrowRight size={16} />
  </div>
</a>
) }
