import React from "react"

interface TableProps {
  description: string
  data: {
    cssClassName: string
    description: string
    usageNotes: string
  }[]
}
const CSSClassesTable = ({ description, data }: TableProps) => {
  return (
    <>
      <p>{description}</p>
      <table
        style={{
          width: "100%",
          borderCollapse: "collapse",
          marginBottom: "24px",
          fontSize: "14px",
        }}
      >
        <thead>
          <tr
            style={{
              backgroundColor: "#f8f9fa",
              borderBottom: "2px solid #dee2e6",
            }}
          >
            <th
              style={{
                padding: "12px",
                textAlign: "left",
                fontWeight: "600",
                border: "1px solid #dee2e6",
              }}
            >
              CSS Class Name
            </th>
            <th
              style={{
                padding: "12px",
                textAlign: "left",
                fontWeight: "600",
                border: "1px solid #dee2e6",
              }}
            >
              Description
            </th>
            <th
              style={{
                padding: "12px",
                textAlign: "left",
                fontWeight: "600",
                border: "1px solid #dee2e6",
              }}
            >
              Usage Notes
            </th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr key={index} style={{ borderBottom: "1px solid #dee2e6" }}>
              <td
                style={{
                  padding: "12px",
                  border: "1px solid #dee2e6",
                  fontFamily: "monospace",
                  backgroundColor: "#f8f9fa",
                }}
              >
                <code>{row.cssClassName}</code>
              </td>
              <td style={{ padding: "12px", border: "1px solid #dee2e6" }}>
                {row.description}
              </td>
              <td style={{ padding: "12px", border: "1px solid #dee2e6" }}>
                {row.usageNotes}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </>
  )
}

export default CSSClassesTable
